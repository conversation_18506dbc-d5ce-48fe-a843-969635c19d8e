import { defineStore } from 'pinia';
import type { GameInfo } from '/#/store';
import { ref, watch } from 'vue';
import { getGameByPageInfo, findGamesByPrefixApi } from '@/api/public-opinion-monitoring';

export const useGameStore = defineStore('game', () => {
  // 游戏信息状态
  const gameInfo = ref<GameInfo>({
    gameId: '',
    gameNameCn: '',
    gameNameEn: '',
    gameIconUrl: '',
    gameIcon: '',
    gameCompany: '',
    remarks: null,
    ext: null,
  });

  // 是否已加载的标记
  const loaded = ref(false);

  // // 监听状态变化并保存到 localStorage
  watch(
    () => gameInfo.value,
    (newValue) => {
      console.log('newValue', newValue);
      localStorage.setItem('gameId', newValue.gameId);
    },
    { deep: true }
  );
  // 设置游戏信息
  const setGameInfo = (info: Partial<GameInfo>) => {
    gameInfo.value = { ...gameInfo.value, ...info };
    loaded.value = true;
  };
  //默认显示游戏内容
  const fetchGameFirstInfo = async () => {
    const para = {
      prefix: '决胜巅峰',
    };
    // const res = await getGameByPageInfo(para);
    const res = await findGamesByPrefixApi(para);

    if (res && Array.isArray(res) && res.length > 0) {
      const data = res[0];
      const gameData = {
        gameId: data.id,
        gameNameCn: data.nameZh,
        gameNameEn: data.nameEn,
        gameIconUrl: data.iconUrl,
        gameIcon: '',
        gameCompany: data.developerName,
        remarks: null,
        ext: null,
      };
      setGameInfo(gameData);
      return gameInfo.value;
    }
  };

  // 获取游戏信息（如果未加载则从API获取）
  const fetchGameInfo = async (para: any) => {
    // console.log('gameCnName', gameCnName);
    try {
      //   let name = '';
      //   if (gameCnName === null) {
      //     name = '';
      //   } else {
      //     name = gameCnName;
      //   }

      // 将旧的参数格式转换为新的前缀格式
      const prefixPara = {
        prefix: para.platformName || '',
      };
      const res = await findGamesByPrefixApi(prefixPara);
      console.log('res', res);
      if (res && Array.isArray(res) && res.length > 0) {
        const data = res[0];
        const gameData = {
          gameId: data.id,
          gameNameCn: data.nameZh,
          gameNameEn: data.nameEn,
          gameIconUrl: data.iconUrl,
          gameIcon: '',
          gameCompany: data.developerName,
          remarks: null,
          ext: null,
        };
        setGameInfo(gameData);
        return gameInfo.value;
      } else {
        gameInfo.value = {
          gameId: '',
          gameNameCn: '',
          gameNameEn: '',
          gameIconUrl: '',
          gameIcon: '',
          gameCompany: '',
          remarks: null,
          ext: null,
        };
      }
    } catch (error) {
      console.error('获取游戏信息失败:', error);
      throw error;
    }
  };

  return {
    gameInfo,
    loaded,
    setGameInfo,
    fetchGameInfo,
    fetchGameFirstInfo,
  };
});
