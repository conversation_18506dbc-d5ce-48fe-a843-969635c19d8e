<template>
    <div class="page">
        <div class="navbar">
            <div class="title">精品报告宝库</div>
            <a-input-search
                v-model:value="searchVal"
                placeholder="请输入关键词"
                
                class="searchBt"
                @search="onSearch"
            />
        </div>

        <div class="content">

            <div class="report" v-for="(report, index) in reports" :key="index" >
                <div class="report-header">
                    <div class="report-title">{{ report.title }}</div>
                    <div class="report-download">
                        <a-button type="default" class="report-download-btn">
                            <DownloadOutlined />下载
                        </a-button>
                    </div>
                </div>
                
                <div class="report-intro">{{ report.intro }}</div>
                <div class="publish-time">发布时间：{{ report.time }}</div>
                <div class="report-footer">
                    <div>发布单位：{{ report.publisher }}</div>
                    <div>{{ report.views }}浏览 | {{ report.downloads }}下载</div>
                </div>
            </div>

        </div>
    </div>
</template>

<script setup>
import { ref } from 'vue'
import { DownloadOutlined } from '@ant-design/icons-vue';

// 报告数据
const reports = ref([
  {
    title: "报告名称",
    intro: "报告正文内容节选，结尾含蓄，点明主题。呼应开头结构完整，可谓点睛之笔。",
    time: "2025/3/5",
    publisher: "王者荣耀官方",
    views: "15036",
    downloads: "306"
  },
  // 复制三份数据（共四份）
  {
    title: "报告名称",
    intro: "报告正文内容节选，结尾含蓄，点明主题。呼应开头结构完整，可谓点睛之笔。",
    time: "2025/3/5",
    publisher: "王者荣耀官方",
    views: "15036",
    downloads: "306"
  },
  {
    title: "报告名称",
    intro: "报告正文内容节选，结尾含蓄，点明主题。呼应开头结构完整，可谓点睛之笔。",
    time: "2025/3/5",
    publisher: "王者荣耀官方",
    views: "15036",
    downloads: "306"
  },
  {
    title: "报告名称",
    intro: "报告正文内容节选，结尾含蓄，点明主题。呼应开头结构完整，可谓点睛之笔。",
    time: "2025/3/5",
    publisher: "王者荣耀官方",
    views: "15036",
    downloads: "306"
  }
]);

</script>

<style lang="scss">
.page {
    background: linear-gradient(135deg, #e0f7ff 0%, #ffffff 100%);
    min-height: 100vh;
}

.navbar {
    padding-top: 5vh;
    text-align: center;
}

.title {
    font-size: 30px;
    color: #407aff;
    font-weight: bolder;
    font-family: Arial, sans-serif;
}

.content {
    background-color: #ffffff;
    margin-top: 1vw;
    box-shadow:
      0px 0px 0px rgba(77, 85, 117, 0.05),
      0px 3px 7px rgba(77, 85, 117, 0.05),
      0px 5px 14px rgba(77, 85, 117, 0.04),
      0px 13px 18px rgba(77, 85, 117, 0.03),
      0px 20px 20px rgba(77, 85, 117, 0.01),
      0px 35px 30px rgba(77, 85, 117, 0);
    margin: 20px;
    border-radius: 10px;
    padding: 20px 40px;
}

.report {
    margin-bottom: 10px;
}

.searchBt {
    width: 40vh;
    margin-top: 5px;
    border-radius: 20px !important;

}

.report-header {
    display: flex;
    justify-content: space-between;
}

.report-title {
    font-weight: bolder;
    font-size: 20px;
}

.report-download-btn {
    border: #1871ff 2px solid;
    color: #1871ff;
    font-weight: 2px;
}

.report-intro {
    font-size: 12px;
    color: #a0a3a6;
    font-weight: 500;
}

.publish-time {
    color: #999c9f;
    margin-top: 5px;
    font-size: 10px;
}

.report-footer {
    display: flex;
    justify-content: space-between;
    color: #999c9f;
    font-size: 10px;
}
</style>