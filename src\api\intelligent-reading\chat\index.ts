import { otherHttp } from '/@/utils/http/axios';
enum Api {
  getChatHistory = '/smartReadingDB/chatHistory',
  getChatList = '/smartReadingDB/chatList',
  getChatCollectionList = '/smartReadingDB/chatCollectionList',
  updateChatCollect = '/smartReadingDB/updateChatCollect',
  deleteChat = '/smartReadingDB/deleteChat',
  getSmartChart = '/smartReadingDB/chat',
}

/**
 * @description: 获取对话历史记录
 */
export function getChatHistoryApi(params) {
  return otherHttp.get({
    url: Api.getChatHistory,
    params,
  });
}

/**
 * @description: 获取对话列表
 */
export function getChatListApi(params) {
  return otherHttp.get({
    url: Api.getChatList,
    params,
  });
}

/**
 * @description: 获取收藏对话列表
 */
export function getChatCollectionListApi(params) {
  return otherHttp.get({
    url: Api.getChatCollectionList,
    params,
  });
}

/**
 *  修改对话收藏状态
 */
export function updateChatCollectApi(params) {
  return otherHttp.put({
    url: Api.updateChatCollect,
    params,
  });
}

/**
 * 删除对话
 */
export function deleteChatApi(params) {
  return otherHttp.delete({
    url: Api.deleteChat,
    params,
  });
}
/**
 * db-gpt获取对话
 */
export function getSmartChartApi(params) {
  return otherHttp.get({
    url: Api.getSmartChart,
    params,
  });
}
