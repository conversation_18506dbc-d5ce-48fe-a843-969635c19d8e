import { otherHttp } from '/@/utils/http/axios';
enum Api {
  getChart = '/smartReading/chart',
  getChatHistory = '/smartReading/chatHistory',
  getChatList = '/smartReading/chatList',
  getChatCollectionList = 'smartReading/chatCollectionList',
  updateChatCollect = 'smartReading/updateChatCollect',
  deleteChat = 'smartReading/deleteChat',
}

/**
 * @description: 获取对话的图表
 */
export function getChatingChartApi(params) {
  return otherHttp.get({
    url: Api.getChart,
    params,
  });
}

/**
 * @description: 获取对话历史记录
 */
export function getChatHistoryApi(params) {
  return otherHttp.get({
    url: Api.getChatHistory,
    params,
  });
}

/**
 * @description: 获取对话列表
 */
export function getChatListApi(params) {
  return otherHttp.get({
    url: Api.getChatList,
    params,
  });
}

/**
 * @description: 获取收藏对话列表
 */
export function getChatCollectionListApi(params) {
  return otherHttp.get({
    url: Api.getChatCollectionList,
    params,
  });
}

/**
 *  修改对话收藏状态
 */
export function updateChatCollectApi(params) {
  return otherHttp.put({
    url: Api.updateChatCollect,
    params,
  });
}

/**
 * 删除对话
 */
export function deleteChatApi(params) {
  return otherHttp.delete({
    url: Api.deleteChat,
    params,
  });
}
/**
 * db-gpt获取对话
 */
export function getSmartChartApi(params) {
  return otherHttp.get({
    url: Api.getSmartChart,
    params,
  });
}
