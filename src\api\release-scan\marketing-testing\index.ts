import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetAllDevice = '/shop/getAllDevice',
  QueryGameByName = '/appInfo/queryByName',
  GetMutDim = '/shop/getMutDim',
}

/**
 * 获取所有设备平台
 */
export function getAllDeviceApi() {
  return defHttp.get({
    url: Api.GetAllDevice,
  });
}

/**
 * 按名称查询游戏
 * @param params 查询参数
 */
export function queryGameByNameApi(params: {
  pageNo: number;
  pageSize: number;
  platformName?: string;
}) {
  return defHttp.get({
    url: Api.QueryGameByName,
    params,
  });
}

/**
 * 获取多维度统计数据
 * @param params 查询参数
 */
export function getMutDimApi(params: {
  publisherId: string;
  sortField: string;
  startTime: string;
  country: string;
  device: string;
}) {
  return defHttp.get({
    url: Api.GetMutDim,
    params,
  });
}