<template>
  <div>
    <div class="chart-container">
      <div class="chart-container1">
        <h2>应用商店排行</h2>
        <a-button
          class="custom-button"
          :class="{ selected: selectedType === 'top_free' }"
          @click="selectedType = 'top_free'"
        >免费下载</a-button>
        <a-button
          class="custom-button"
          :class="{ selected: selectedType === 'top_paid' }"
          @click="selectedType = 'top_paid'"
        >付费下载</a-button>
        <a-button
          class="custom-button"
          :class="{ selected: selectedType === 'top_grossing' }"
          @click="selectedType = 'top_grossing'"
        >净收入</a-button>
      </div>
      <div class="chart-container1">
        <!-- 日期选择 -->
        <a-space direction="vertical" :size="12" style="margin-left: 2%;">
          <a-range-picker :presets="rangePresets" v-model:value="selectedRange" />
        </a-space>
        <!-- 国家选择 -->
        <a-select
          v-model:value="selectedCountry"
          mode="multiple"
          allowClear
          placeholder="请选择国家"
          style="width: 15vw;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleCountryChange"
        >
          <a-select-option value="all" @click="selectAllCountries">选择全部</a-select-option>
          <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
            {{ country.label }}
          </a-select-option>
        </a-select>
        <!-- 平台选择 -->
        <a-select
          v-model:value="selectedDevice"
          mode="multiple"
          allowClear
          placeholder="全部平台"
          style="width: 15vw;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleDeviceChange"
        >
          <a-select-option value="all" @click="selectAllDevices">选择全部</a-select-option>
          <a-select-option v-for="device in devices" :key="device.value" :value="device.value">
            {{ device.label }}
          </a-select-option>
        </a-select>
        <!-- 查询按钮 -->
        <a-button type="primary" @click="handleQuery" style="margin-left: 20px;">查询</a-button>
      </div>
      <div style="position: relative;">
        <div ref="chartRef" style="width: 100%; height: 400px; margin: auto;"></div>
        <div
          v-if="!hasChartData"
          class="empty-data-container"
          style="
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            height: 400px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            pointer-events: none;
            background: #fff;
            border-radius: 4px;
          "
        >
          <i class="empty-icon">🔍</i>
          <p>没有找到匹配的数据</p>
          <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
        </div>
      </div>
    </div>
    <!-- 表格 -->
    <div class="chart-container">
      <h2>游戏活跃度分析</h2>
      <a-table
        :columns="columns"
        :data-source="tableData"
        :pagination="false"
        :scroll="{x: 'max-content'}"
        size="middle"
        :bordered="true"
        :row-class-name="(_record, index) => (index % 2 === 1 ? 'table-striped' : null)"
        class="custom-table"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'iconUrl'">
            <img :src="record.iconUrl" style="width: 40px; height: 40px; border-radius: 4px;" />
          </template>
          <template v-else-if="column.dataIndex === 'nameZh'">
            <div style="display: flex; align-items: center; gap: 8px;">
              <img :src="record.iconUrl" style="width: 40px; height: 40px; border-radius: 4px;" />
              <span>{{ record.nameZh || '-' }}</span>
            </div>
          </template>
          <template v-else-if="column.dataIndex === 'categories'">
            <a-tag color="blue">{{ record.categories || '-' }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'status'">
            <a-tag :color="record.status === '上架' ? 'green' : 'red'">{{ record.status || '-' }}</a-tag>
          </template>
          <template v-else-if="column.dataIndex === 'downloadTimes'">
            {{ record.downloadTimes ? formatNumber(record.downloadTimes) : '-' }}
          </template>
          <template v-else-if="column.dataIndex === 'income'">
            {{ record.income ? formatNumber(record.income) : '-' }}
          </template>
          <template v-else-if="column.dataIndex === 'downloadTimeRate'">
            <span v-if="record.downloadTimeRate !== undefined && record.downloadTimeRate !== null" :style="{ color: record.downloadTimeRate >= 0 ? '#52c41a' : '#f5222d' }">
              {{ record.downloadTimeRate >= 0 ? '+' : '' }}{{ record.downloadTimeRate }}%
            </span>
            <span v-else>-</span>
          </template>
          <template v-else-if="column.dataIndex === 'incomeRate'">
            <span v-if="record.incomeRate !== undefined && record.incomeRate !== null" :style="{ color: record.incomeRate >= 0 ? '#52c41a' : '#f5222d' }">
              {{ record.incomeRate >= 0 ? '+' : '' }}{{ record.incomeRate }}%
            </span>
            <span v-else>-</span>
          </template>
          <template v-else>
            {{ record[column.dataIndex] || '-' }}
          </template>
        </template>
        <template #emptyText>
          <div class="empty-data-container">
            <i class="empty-icon">🔍</i>
            <p>没有找到匹配的数据</p>
            <p class="empty-data-tip">请尝试调整筛选条件后再次查询</p>
          </div>
        </template>
      </a-table>
    </div>

    <!-- 全球热力图容器 -->
    <div class="chart-container" style="margin-top: 20px;">
      <div class="chart-containertitle">
        <h2>全球热度分布</h2>
      </div>
      <!-- 筛选条件区域 -->
      <div class="filter-container" style="margin-bottom: 20px; display: flex; align-items: center; gap: 16px;">
        <!-- 游戏选择 -->
        <div style="display: flex; align-items: center; gap: 8px;">
          <a-select
            v-model:value="selectedGame"
            show-search
            placeholder="选择游戏"
            style="width: 200px"
            :default-active-first-option="false"
            :show-arrow="true"
            :filter-option="false"
            :not-found-content="gameLoading ? '加载中...' : '未找到'"
            @search="onGameSearch"
            @popupScroll="onGamePopupScroll"
          >
            <a-select-option v-for="game in gameOptions" :key="game.id" :value="game.id">
              <div style="display: flex; align-items: center; gap: 8px;">
                <img :src="game.iconUrl" style="width: 24px; height: 24px; border-radius: 4px;" />
                <span>{{ game.nameZh }}</span>
              </div>
            </a-select-option>
          </a-select>
          <!-- 已选游戏展示 -->
          <div v-if="selectedGameInfo" style="display: flex; align-items: center; gap: 8px; padding: 4px 8px; background: #f5f5f5; border-radius: 4px;">
            <img :src="selectedGameInfo.iconUrl" style="width: 24px; height: 24px; border-radius: 4px;" />
            <span>{{ selectedGameInfo.nameZh }}</span>
          </div>
        </div>

        <!-- 日期选择 -->
        <a-range-picker
          v-model:value="heatmapDateRange"
          :presets="rangePresets"
          @change="onHeatmapDateChange"
          style="width: 260px;"
        />

        <!-- 国家选择 -->
        <a-select
          v-model:value="selectedCountry1"
          mode="multiple"
          allowClear
          placeholder="请选择国家"
          style="width: 15vw;"
          :max-tag-count="1"
          :max-tag-placeholder="maxTagPlaceholder"
          @change="handleCountryChange"
        >
          <a-select-option value="all" @click="selectAllCountries1">选择全部</a-select-option>
          <a-select-option v-for="country in countries" :key="country.value" :value="country.value">
            {{ country.label }}
          </a-select-option>
        </a-select>
        <!-- 展示数据选择 -->
        <a-select
          v-model:value="selectedDataType"
          placeholder="选择展示数据"
          style="width: 200px"
          @change="onDataTypeChange"
        >
          <a-select-option value="下载量">下载量</a-select-option>
          <a-select-option value="收入">收入</a-select-option>
          <!-- <a-select-option value="active">活跃度</a-select-option> -->
        </a-select>

        <!-- 查询按钮 -->
        <a-button type="primary" @click="fetchHeatmapData">查询</a-button>
      </div>
      <div ref="heatmapRef" style="width: 100%; height: 400px;"></div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, h } from 'vue';
import * as echarts from 'echarts';
import dayjs, { Dayjs } from 'dayjs';
import type { TreeSelectProps } from 'ant-design-vue';
import { TreeSelect } from 'ant-design-vue';
import type { CascaderProps } from 'ant-design-vue';
import { Cascader } from 'ant-design-vue';
import mihayoImg from '@/views/release-scan/download-analysis/image/mihayo.png';
import { BasicTable, useTable } from '/@/components/Table';
import { message } from 'ant-design-vue';
import { defHttp } from '/@/utils/http/axios';
import { findGamesByPrefixApi } from '@/api/public-opinion-monitoring';
import worldJson from './world.json';

// 注册世界地图数据
echarts.registerMap('world', worldJson as { type: 'FeatureCollection', features: any[] });

// 查询类型
const selectedType = ref<'top_free' | 'top_paid' | 'top_grossing'>('top_free');

// 日期、国家、平台
const selectedRange = ref<RangeValue>([dayjs().add(-1, 'year'), dayjs()]);
const selectedCountry = ref<string[]>([]);
  const selectedCountry1 = ref<string[]>([]);
const selectedDevice = ref<string[]>([]);
const countries = [
  { value: '菲律宾', label: '菲律宾' },
  { value: '柬埔寨', label: '柬埔寨' },
  { value: '马来西亚', label: '马来西亚' },
  { value: '泰国', label: '泰国' },
  { value: '文莱', label: '文莱' },
  { value: '新加坡', label: '新加坡' },
  { value: '印度尼西亚', label: '印度尼西亚' },
  { value: '越南', label: '越南' },
  { value: '缅甸', label: '缅甸' },
  { value: '中国台湾', label: '中国台湾' },
  { value: '老挝人民民主共和国', label: '老挝人民民主共和国' },
];
const devices = ref<{ value: string; label: string }[]>([]);
const fetchDevices = async () => {
  try {
    const res = await defHttp.get({ url: '/shop/getAllDevice' });
    // 处理返回值，将apple转换为App Store，并添加"选择全部"选项
    const deviceOptions = (res || []).map((item: any) => ({
      value: item.value,
      label: item.value === 'apple' ? 'App Store' : item.value,
    }));
    
    // 添加"选择全部"选项
    devices.value = [

      ...deviceOptions,
    ];
  } catch (e) {
    devices.value = [

    ];
  }
};
onMounted(() => {
  fetchDevices();
  if (chartRef.value) {
    chart = echarts.init(chartRef.value);
  }
  
  // 初始化热力图
  initHeatmap();
  loadGameOptions();
});

// 表格
const columns = ref([
{
    title: '排名',
    dataIndex: 'ranking',
    key: 'ranking',
    width: 80,
    align: 'center',
  },
  {
    title: '应用',
    dataIndex: 'nameZh',
    key: 'nameZh',
    width: 300,
  },
  { title: '发行商', dataIndex: 'developerId', key: 'developerId' },
  { title: '点击量', dataIndex: 'downloadTimes', key: 'downloadTimes' },
  { title: '年涨幅', dataIndex: 'yearlyGrowth', key: 'yearlyGrowth' },
  { title: '月涨幅', dataIndex: 'monthlyGrowth', key: 'monthlyGrowth' },
  { title: '周涨幅', dataIndex: 'weeklyGrowth', key: 'weeklyGrowth' },
  { title: '日涨幅', dataIndex: 'dailyGrowth', key: 'dailyGrowth' },
]);
const tableData = ref([]);

// 图表
const chartRef = ref<HTMLElement | null>(null);
const heatmapRef = ref<HTMLElement | null>(null);
let chart: echarts.ECharts | null = null;
let heatmap: echarts.ECharts | null = null;
const hasChartData = ref(false);

// 查询
const handleQuery = async () => {
  try {
    const params = {
      startDate: selectedRange.value[0]?.format ? selectedRange.value[0].format('YYYY-MM-DD') : selectedRange.value[0],
      endDate: selectedRange.value[1]?.format ? selectedRange.value[1].format('YYYY-MM-DD') : selectedRange.value[1],
      countryName: selectedCountry.value,
      deviceName: selectedDevice.value,
      sortField: selectedType.value,
    };
    
    const res = await defHttp.get({ url: '/shop/shopBehavior', params });
    const list = res.appstoreRankingsPaid || [];
    if (list && list.length > 0) {
      hasChartData.value = true;
      if (chart) {
        renderChart(list);
      }
      tableData.value = list;
    } else {
      hasChartData.value = false;
      if (chart) {
        chart.clear();
      }
    }
  } catch (e) {
    message.error('查询失败');
    hasChartData.value = false;
    if (chart) {
      chart.clear();
    }
  }
};

// 图表渲染逻辑
function renderChart(list: any[] = []) {
  if (!chart) return;
  const xData = list.map(item => item.nameZh || item.app || item.name);
  const yData = list.map(item => item.downloadTimes || item.value || 0);
  chart.setOption({
    xAxis: {
      data: xData,
      axisLabel: {
        interval: 0,
        rotate: 30,
        fontSize: 12,
        formatter: function (value: string) {
          // 最多显示6个字符，超出部分用省略号
          return value && value.length > 6 ? value.slice(0, 10) + '...' : value;
        }
      }
    },
    yAxis: { name: '下载量' },
    series: [{ type: 'bar', data: yData, barWidth: 40, color: '#0893CF' }],
  });
}

// 时间选择快捷
type RangeValue = [Dayjs, Dayjs];
const rangePresets = ref([
  { label: '当天', value: [dayjs().add(-1, 'd'), dayjs()] },
  { label: '最近三天', value: [dayjs().add(-3, 'd'), dayjs()] },
  { label: '最近一周', value: [dayjs().add(-7, 'd'), dayjs()] },
  { label: '最近一个月', value: [dayjs().add(-1, 'month'), dayjs()] },
  { label: '最近三个月', value: [dayjs().add(-3, 'month'), dayjs()] },
  { label: '最近六个月', value: [dayjs().add(-6, 'month'), dayjs()] },
  { label: '最近一年', value: [dayjs().add(-1, 'year'), dayjs()] },
  { label: '最近两年', value: [dayjs().add(-2, 'year'), dayjs()] },
  { label: '最近三年', value: [dayjs().add(-3, 'year'), dayjs()] },
]);

// 格式化数字
function formatNumber(value: number): string {
  if (value >= 1000000) {
    return (value / 1000000).toFixed(2) + 'M';
  } else if (value >= 1000) {
    return (value / 1000).toFixed(2) + 'K';
  } else {
    return value.toString();
  }
}

// 初始化热力图
const initHeatmap = () => {
  if (!heatmapRef.value) return;
  
  heatmap = echarts.init(heatmapRef.value);
  
  const chart = echarts.init(heatmapRef.value);
  chart.setOption({
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}'
    },
    visualMap: {
      min: 0,
      max: 100,
      text: ['高', '低'],
      realtime: false,
      calculable: true,
      inRange: {
        color: ['#e0f3f8', '#abd9e9', '#74add1', '#4575b4', '#313695']
      }
    },
    series: [{
      name: '全球热度分布',
      type: 'map',
      map: 'world',
      roam: true,
      emphasis: {
        label: {
          show: true
        }
      },
      data: [] // 初始为空数组，数据将通过API获取
    }]
  });
};

// 监听窗口大小变化
window.addEventListener('resize', () => {
  chart?.resize();
  heatmap?.resize();
});

// 热力图相关状态
const selectedGame = ref(undefined);
const selectedGameInfo = ref<any>(null);
const gameOptions = ref<any[]>([]);
const gameLoading = ref(false);
const gamePage = ref(1);
const gamePageSize = ref(10);
const gameTotal = ref(0);
const gameSearchValue = ref('');

const heatmapDateRange = ref<[Dayjs, Dayjs] | null>(null);
const selectedDataType = ref('下载量');

// 添加国家名称映射
const countryNameMap: { [key: string]: string } = {
  '泰国': 'Thailand',
  '马来西亚': 'Malaysia',
  '中国台湾': 'Taiwan',
  '新加坡': 'Singapore',
  '印度尼西亚': 'Indonesia',
  '越南': 'Vietnam',
  '菲律宾': 'Philippines',
  '柬埔寨': 'Cambodia',
  '文莱': 'Brunei',
  '缅甸': 'Myanmar',
  '老挝人民民主共和国': 'Laos'
};

// 游戏搜索
const onGameSearch = async (value: string) => {
  gameSearchValue.value = value;
  await loadGameOptions();
};

// 加载游戏选项
const loadGameOptions = async () => {
  gameLoading.value = true;
  try {
    const res = await findGamesByPrefixApi({
      prefix: gameSearchValue.value || '',
    });
    // 处理返回的数据
    const newGames = (res || []).map((item: any) => ({
      id: item.id,
      nameZh: item.nameZh,
      iconUrl: item.iconUrl,
      // 其他需要的字段...
    }));
    // 前缀树API返回所有匹配结果，直接替换
    gameOptions.value = newGames;
    gameTotal.value = newGames.length;
  } catch (e) {
    message.error('加载游戏列表失败');
  } finally {
    gameLoading.value = false;
  }
};

// 游戏下拉框滚动加载
const onGamePopupScroll = (e: Event) => {
  const target = e.target as HTMLElement;
  if (target.scrollTop + target.offsetHeight >= target.scrollHeight - 20) {
    if (gameOptions.value.length < gameTotal.value) {
      gamePage.value++;
      loadGameOptions();
    }
  }
};

// 游戏选择变化
const onGameChange = (value: string) => {
  const selectedGame = gameOptions.value.find(game => game.id === value);
  if (selectedGame) {
    selectedGameInfo.value = {
      id: selectedGame.id,
      nameZh: selectedGame.nameZh,
      iconUrl: selectedGame.iconUrl
    };
    fetchHeatmapData();
  }
};

// 日期变化
const onHeatmapDateChange = () => {
  fetchHeatmapData();
};

// 国家变化
const onCountryChange = () => {
  fetchHeatmapData();
};

// 数据类型变化
const onDataTypeChange = () => {
  fetchHeatmapData();
};

// 获取热力图数据
const fetchHeatmapData = async () => {
  console.log(selectedGame)
  try {
    const response = await defHttp.get({
      url: '/shop/analysisDownloadsIncomeByAppId',
      params: {
        publisherId: selectedGame.value,
        startTime: heatmapDateRange.value?.[0]?.format('YYYY-MM-DD'),
        endDate: heatmapDateRange.value?.[1]?.format('YYYY-MM-DD'),
        country: selectedCountry.value,
        sortOrder: selectedDataType.value,
      },
    });

    if (response) {
      // 转换数据格式，将中文国家名称转换为英文
      const heatmapData = Object.entries(response).map(([country, value]) => ({
        name: countryNameMap[country] || country, // 使用映射表转换国家名称
        value: value as number
      }));
      
      // 更新热力图数据
      if (heatmapRef.value) {
        const chart = echarts.getInstanceByDom(heatmapRef.value);
        if (chart) {
          chart.setOption({
            series: [{
              data: heatmapData
            }]
          });
        }
      }
    }
  } catch (error) {
    console.error('获取热力图数据失败:', error);
  }
};

// 监听日期范围变化
watch(heatmapDateRange, () => {
  fetchHeatmapData();
}, { deep: true });

// 监听数据类型变化
watch(selectedDataType, () => {
  fetchHeatmapData();
});

// 监听国家选择变化
watch(selectedCountry, () => {
  fetchHeatmapData();
});

// 在组件挂载时获取数据
onMounted(() => {
  fetchHeatmapData();
});

// 添加标签溢出处理函数
const maxTagPlaceholder = (omittedValues: any[]) => {
  return h('span', { class: 'ellipsis-tag' }, '...');
};

// 添加选择全部和处理函数
const selectAllCountries = () => {
  selectedCountry.value = countries.map(item => item.value);
};
const selectAllCountries1 = () => {
  selectedCountry1.value = countries.map(item => item.value);
};
const handleCountryChange = (value: string[]) => {
  if (value.includes('all')) {
    selectedCountry.value = countries.map(item => item.value);
  }
};

const selectAllDevices = () => {
  selectedDevice.value = devices.value.map(item => item.value);
};

const handleDeviceChange = (value: string[]) => {
  if (value.includes('all')) {
    selectedDevice.value = devices.value.map(item => item.value);
  }
};
</script>

<style lang="scss" scoped>
.chart-container {
  border: 5px;
  margin: 10px 10px;
  height: auto;
  min-height: 400px;
  background-color: white;
  padding: 20px;
  border-radius: 8px;
  overflow: visible;
  box-shadow:
    0px 0px 0px rgba(77, 85, 117, 0.05),
    0px 3px 7px rgba(77, 85, 117, 0.05),
    0px 5px 14px rgba(77, 85, 117, 0.04),
    0px 13px 18px rgba(77, 85, 117, 0.03),
    0px 20px 20px rgba(77, 85, 117, 0.01),
    0px 35px 30px rgba(77, 85, 117, 0);
}
.table-container {
  margin: 24px 2% 0 10px;
  background: #fff;
  border-radius: 10px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.05);
  padding: 24px;
}

.custom-table {
  width: 100%;
  font-size: 14px;
}

/* 表头样式 */
:deep(.custom-table .ant-table-thead > tr > th) {
  background-color: #c2e8f8;
  color: #333;
  font-weight: bold;
  text-align: center;
  padding: 12px 16px;
}

/* 表格单元格样式 */
:deep(.custom-table .ant-table-tbody > tr > td) {
  padding: 12px 16px;
  text-align: center;
  color: #666;
  vertical-align: middle;
  border-bottom: 1px solid #eee;
}

/* 斑马纹样式 */
:deep(.custom-table .ant-table-tbody > tr:nth-child(odd)) {
  background-color: #ffffff;
}
:deep(.custom-table .ant-table-tbody > tr:nth-child(even)) {
  background-color: #dcf2fb;
}

/* 悬停样式 */
:deep(.custom-table .ant-table-tbody > tr:hover > td) {
  background-color: #e6f7ff;
}

.chart-container1 {
    display: flex;
    /* 使用 flex 布局 */
    gap: 10px;
    /* 设置元素之间的间距 */
}

.chart-container h2 {
    border: 3px solid #0893CF;
    /* 2px 宽度的 #0893CF 色边框 */
    border-top: none;
    /* 取消上边框 */
    border-right: none;
    /* 取消右边框 */
    border-bottom: none;
    /* 取消下边框 */
    padding-left: 10px;
    /* 可选：左边内边距，使文字与左边框有一定距离 */
    margin-bottom: 20px;
    /* 可选：下边距，使标题与图表有一定距离 */
}

.custom-button {
    border: 2px solid #ccc;
    /* 未选中时的灰色边框 */
    color: #333;
    /* 未选中时的字体颜色 */
    transition: border-color 0.3s, color 0.3s;
    /* 添加过渡效果 */
}

.custom-button.selected {
    border-color: #0893CF;
    /* 选中时的边框颜色 */
    color: #0893CF;
    /* 选中时的字体颜色 */
}

:deep(.ant-table-title) {
    margin-top: -40px;
}
.empty-data-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10px;
}

.empty-icon {
  font-size: 32px;
  color: #ccc;
  margin-bottom: 10px;
}

.empty-data-container p {
  margin: 0;
  font-size: 16px;
  color: #666;
}

.empty-data-tip {
  font-size: 14px;
  color: #999;
}
</style>
