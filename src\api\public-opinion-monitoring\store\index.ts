import { defHttp } from '/@/utils/http/axios';

enum Api {
  getOpinionStoreScore = '/opinionStore/score',
  getOpinionStoreTrend = '/opinionStore/trend',
  getOpinionStoreCommentScore = '/opinionStore/commentScore',
  getOpinionStoreCommentDay = '/opinionStore/commentDay',
  getOpinionStoreCountry = '/country/getAll',
  getOpinionStoreDevice = '/shop/getAllDevice',

}

/**
 * @description: 获取商店评分
 */
export function getOpinionStoreScoreApi(params) {
  return defHttp.get({
    url: Api.getOpinionStoreScore,
    params,
  });
}
/**
 * @description: 评分趋势
 */
export function getOpinionStoreTrendApi(params) {
  return defHttp.get({
    url: Api.getOpinionStoreTrend,
    params,
  });
}
/**
 * @description: 评论详情-评分最高
 */
export function getOpinionStoreCommentScoreApi(params) {
  return defHttp.get({
    url: Api.getOpinionStoreCommentScore,
    params,
  });
}
/**
 * @description: 评论详情-最新评论
 */
export function getOpinionStoreCommentDayApi(params) {
  return defHttp.get({
    url: Api.getOpinionStoreCommentDay,
    params,
  });
}
/**
 * @description: 获取全部国家信息
 */
export function getOpinionStoreCountryApi(params) {
  return defHttp.get({
    url: Api.getOpinionStoreCountry,
    params,
  });
}
/**
 * @description: 获取全部商店信息
 */
export function getOpinionStoreDeviceApi(params) {
  return defHttp.get({
    url: Api.getOpinionStoreDevice,
    params,
  });
}