import { defHttp } from '/@/utils/http/axios';

enum Api {
  getAppList = '/appInfo/list',
  getAllGenre = '/shop/getAllGenre',
  queryAppByName = '/appInfo/queryByName',
}

/**
 * @description: 获取应用列表
 * @param params 请求参数
 * @returns 应用列表数据
 */
export function getAppListApi(params) {
  return defHttp.get({
    url: Api.getAppList,
    params,
  });
}

/**
 * @description: 获取所有游戏类型
 * @returns 游戏类型列表数据
 */
export function getAllGenreApi() {
  return defHttp.get({
    url: Api.getAllGenre,
  });
}

/**
 * @description: 根据名称查询应用
 * @param params 请求参数
 * @returns 应用查询结果
 */
export function queryAppByNameApi(params) {
  return defHttp.get({
    url: Api.queryAppByName,
    params,
  });
}