<script setup lang="ts">
  import logoUrl from '@/assets/images/default-game.png';
  import { useRouter } from 'vue-router';
  import { findGamesByPrefixApi, getGameByPageInfo } from '@/api/public-opinion-monitoring';
  import { ref, onMounted, defineEmits, PropType, watch } from 'vue';
  import { getPlanTableListByUserIdApi } from '@/api/public-opinion-monitoring';
  import { useUserStore } from '/@/store/modules/user';
  import { useGameStore } from '/@/store/modules/gameStore';
  import { usePlanStore } from '@/store/modules/planStore';
  import { debounce } from 'lodash-es';

  // import { useGlobSetting } from '@/hooks/setting';

  interface PlanTable {
    id: number;
    gameId: string;
    userId: string;
    planName: string;
    expireTime: number;
    spiderIds: {
      channelCustomNameList: string[];
    };
    includeKeywords: {
      includeKeywords: string[];
    };
    excludeKeywordsStr: string | null;
    ext: any;
  }
  interface MockVal {
    value: string;
  }

  interface GameOption {
    id: string;
    label: string;
    value: string;
    iconUrl: string;
  }
  const props = defineProps({
    showSearch: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
    showPlanSection: {
      type: Boolean as PropType<boolean>,
      default: true,
    },
  });

  const router = useRouter();
  const userStore = useUserStore();
  const gameStore = useGameStore();
  const userId = userStore.getUserInfo?.id?.toString();

  const emit = defineEmits(['gameInfoUpdate', 'planUpdate']);
  const gameCnName = ref<string | null>(null);

  const gameInfo = ref({
    gameId: '',
    gameNameCn: '',
    gameNameEn: '',
    gameIconUrl: '',
    gameIcon: '',
    gameCompany: '',
    remarks: null as string | null,
    ext: null as any,
  });
  //游戏搜索
  const gameOptions = ref<GameOption[]>([]);
  const hasGameMore = ref(true);
  const gameLoading = ref(false);
  const currentPage = ref(1);
  const pageSize = ref(100);

  const planStore = usePlanStore();
  // 监控方案列表
  const planList = ref<PlanTable[]>([]);
  // 选中的方案ID
  const selectedPlanId = ref('');
  //检索游戏 - 用户手动选择游戏时触发
  const selectSearch = () => {
    if (gameCnName.value) {
      fetchGameInfo();
    }
  };
  //初始化游戏
  const getFirstGame = async () => {};
  const fetchGameFirstInfo = async () => {
    await gameStore.fetchGameFirstInfo();
    gameInfo.value = gameStore.gameInfo;
  };
  // 清理上一个游戏的遗留数据
  const clearPreviousGameData = () => {
    // 使用planStore的清理方法
    planStore.clearGameData();

    // 清理本地组件状态
    selectedPlanId.value = '';
    planList.value = [];

    // 触发planId清空事件，通知其他组件清理相关数据
    window.dispatchEvent(new CustomEvent('planIdChanged', { detail: '' }));
  };

  // 获取游戏信息
  const fetchGameInfo = async () => {
    try {
      // 先清理上一个游戏的遗留数据
      // clearPreviousGameData();
      let para = {
        pageNo: currentPage.value || '',
        pageSize: pageSize.value,
        platformName: gameCnName.value,
      };
      await gameStore.fetchGameInfo(para);
      gameInfo.value = gameStore.gameInfo;

      // 触发游戏信息更新事件
      emit('gameInfoUpdate', { gameId: gameInfo.value.gameId });

      // 触发自定义事件，通知同一窗口内的其他组件
      window.dispatchEvent(new CustomEvent('gameIdChanged', { detail: gameInfo.value.gameId }));

      await fetchPlanList(); // 再获取方案列表
    } catch (error) {
      // 获取游戏信息异常，静默处理
    }
  };

  // 获取监控方案列表
  const fetchPlanList = async () => {
    try {
      const gameId = gameInfo.value.gameId;
      if (!userId) {
        return;
      } else {
        let para = {
          userId: userId,
          gameId: gameId,
        };
        const res = await getPlanTableListByUserIdApi(para);
        // 更新监控方案列表
        planList.value = res.planTableList;

        // // 重置planId相关状态
        // planStore.selectedPlanId = null;
        // selectedPlanId.value = '';

        // 如果有方案列表，默认选中第一个
        if (planList.value.length > 0) {
          planStore.selectedPlanId = planList.value[0].id;
          selectedPlanId.value = planList.value[0].id;
          // 保存到localStorage
          localStorage.setItem('currentPlanId', selectedPlanId.value);
          // 触发方案更新事件
          emit('planUpdate', { planId: selectedPlanId.value, gameId: gameId });
          // 触发planId变化事件
          window.dispatchEvent(new CustomEvent('planIdChanged', { detail: String(selectedPlanId.value) }));
        } else {
          // 如果没有方案，确保localStorage也被清空
          localStorage.removeItem('currentPlanId');
          // 触发空planId事件
          emit('planUpdate', { planId: '', gameId: gameId });
          window.dispatchEvent(new CustomEvent('planIdChanged', { detail: '' }));
        }
      }
    } catch (error: any) {
      // 获取监控方案列表异常，静默处理
    }
  };

  // 处理方案选择变化
  const handlePlanChange = (value) => {
    planStore.selectedPlanId = value;
    selectedPlanId.value = value;

    // 保存到localStorage，以便其他组件可以访问
    localStorage.setItem('currentPlanId', String(value));

    // 触发方案更新事件，将 id 作为 planId 传递
    emit('planUpdate', { planId: value, gameId: gameInfo.value.gameId });

    // 触发自定义事件，通知同一窗口内的其他组件
    window.dispatchEvent(new CustomEvent('planIdChanged', { detail: String(value) }));
  };

  // 配置监控方案方法
  const configurePlan = () => {
    router.push({ name: 'config-monitor-plan' });
  };

  // 处理图片加载错误
  const handleImageError = (e: Event) => {
    if (e.target instanceof HTMLImageElement) {
      e.target.src = logoUrl;
    }
  };
  // const options = ref([]);
  const fetching = ref(false);
  // const handleSearch = async (value) => {
  //   let para = {
  //     prefix: value,
  //   };
  //   fetching.value = true;
  //   const newOptions = await findGamesByPrefixApi(para);
  //   if (newOptions && Array.isArray(newOptions)) {
  //     const data = newOptions.map((game) => ({
  //       label: game.nameZh,
  //       value: game.nameZh,
  //       iconUrl: game.iconUrl,
  //     }));
  //     options.value = data;
  //   }
  //   fetching.value = false;
  // };
  onMounted(async () => {
    //确认是否有游戏信息，如果有则现在已存储游戏，若没有则获取第一条游戏信息
    if (gameStore.gameInfo.gameId !== '') {
      gameInfo.value = gameStore.gameInfo;
      // 设置搜索框的值为当前游戏名称
      gameCnName.value = gameStore.gameInfo.gameNameCn;
      fetchPlanList();
      // 加载与当前游戏相关的选项
      loadOptions();
    } else {
      await fetchGameFirstInfo(); // 先获取游戏信息
      // 设置搜索框的值为当前游戏名称
      gameInfo.value = gameStore.gameInfo;
      gameCnName.value = gameStore.gameInfo.gameNameCn;
      fetchPlanList();
      // 加载与当前游戏相关的选项
      loadOptions();
    }
    selectedPlanId.value = planStore.selectedPlanId ? String(planStore.selectedPlanId) : '';
    // if (gameInfo.value.gameId) {
    //   fetchPlanList(); // 再获取方案列表
    // }
  });

  /** 1. 定义模板 ref */
  const page = ref<HTMLElement | null>(null);

  /** 2. 提供给 antd 的挂载点函数 */
  const getPopupContainer = () => page.value!;

  const handleSearch = debounce((value) => {
    gameCnName.value = value;
    gameOptions.value = [];
    loadOptions();
    // 移除自动选择游戏的逻辑，只加载搜索选项
  }, 500);

  // 处理清空搜索框
  const handleClear = () => {
    gameCnName.value = null;
    gameOptions.value = [];
    // 清空后重新加载默认选项
    loadOptions();
  };
  const handleScroll = (e: any) => {
    // 前缀树API不支持分页，所以不需要滚动加载更多
    // 保留函数以避免模板报错，但不执行任何操作
  };
  const loadOptions = async () => {
    if (gameLoading.value) return;
    gameLoading.value = true;
    try {
      fetching.value = true;
      let newOptions;

      // 如果有搜索关键词，使用前缀搜索；否则使用空前缀获取所有游戏
      if (gameCnName.value) {
        // 有搜索关键词时使用前缀搜索
        let para = {
          prefix: gameCnName.value,
        };
        newOptions = await findGamesByPrefixApi(para);
      } else {
        // 没有搜索关键词时，先尝试使用分页API，如果失败则使用空前缀
        try {
          let para = {
            pageNo: 1,
            pageSize: 50, // 每页50个游戏，可以根据需要调整
            platformName: '', // 添加 platformName 参数，空字符串表示获取所有平台
          };
          newOptions = await getGameByPageInfo(para);
        } catch (error) {
          // 如果分页API失败，回退到使用空前缀的前缀搜索
          let para = {
            prefix: '',
          };
          newOptions = await findGamesByPrefixApi(para);
        }
      }

      if (newOptions && newOptions.records && Array.isArray(newOptions.records)) {
        const data = newOptions.records.map((game) => ({
          id: game.id,
          label: game.nameZh,
          value: game.nameZh,
          iconUrl: game.iconUrl,
        }));

        // 只有在没有搜索关键词时，才确保当前游戏在选项中
        if (!gameCnName.value && gameInfo.value.gameNameCn && !data.some(item => item.value === gameInfo.value.gameNameCn)) {
          data.unshift({
            id: gameInfo.value.gameId,
            label: gameInfo.value.gameNameCn,
            value: gameInfo.value.gameNameCn,
            iconUrl: gameInfo.value.gameIconUrl,
          });
        }

        gameOptions.value = data;
        hasGameMore.value = false;
      } else {
        gameOptions.value = [];
        hasGameMore.value = false;
      }
    } catch (error) {
      // 加载选项失败，静默处理
    } finally {
      gameLoading.value = false;
      fetching.value = false;
    }
  };
  //监控plan数组如果为空则赋值planid为空
  watch(
    () => planList.value,
    (newValue) => {
      if (newValue.length === 0) {
        selectedPlanId.value = '';
      }
    }
  );
</script>

<template>
  <div>
    <div class="public-wrapper" ref="page">
      <!-- 顶部栏 -->
      <div class="top-bar">
        <div class="game-info">
          <img :src="gameInfo.gameIconUrl" alt="游戏logo" class="logo" @error="handleImageError" />
          <div class="game-details">
            <h2 class="game-title">{{ gameInfo.gameNameCn }}</h2>
            <p class="game-subtitle">{{ gameInfo.gameCompany }}</p>
          </div>
        </div>
        <!-- 竖线 -->
        <div class="vertical-line"></div>
        <!-- 搜索栏 -->
        <div class="search-bar">
          <div class="search-container">
            <a-select
              show-search
              allow-clear
              v-model:value="gameCnName"
              placeholder="搜索游戏"
              :filter-option="false"
              :not-found-content="null"
              @search="handleSearch"
              @change="selectSearch"
              @clear="handleClear"
              @popup-scroll="handleScroll"
              class="search-input"
              size="large"
              style="width: 100%"
              :get-popup-container="getPopupContainer"
              popupClassName="maxHeight: '250px', overflow: 'auto'"
            >
              <!--              <template #option="{ label, iconUrl }">-->
              <!--                <div style="display: flex; align-items: center">-->
              <!--                  <img :src="iconUrl" style="width: 24px; height: 24px; margin-right: 8px" v-if="iconUrl" />-->
              <!--                  <span>{{ label }}</span>-->
              <!--                </div>-->
              <!--              </template>-->
              <a-select-option v-for="item in gameOptions" :key="item.id" :value="item.label">
                <div style="display: flex; align-items: center">
                  <img :src="item.iconUrl" style="width: 24px; height: 24px; margin-right: 8px" v-if="item.iconUrl" />
                  <span>{{ item.label }}</span>
                </div>
              </a-select-option>
              <a-select-option v-if="gameLoading" key="loading" disabled>
                <a-spin size="small" />
              </a-select-option>
            </a-select>
            <!--            <input type="text" placeholder="搜索游戏" class="search-input" v-model="gameCnName" @keyup.enter="handleSearch" />-->
          </div>
        </div>

        <!-- 方案栏 -->
        <div class="prog-bar" v-if="props.showPlanSection">
          <a-select
            v-model:value="selectedPlanId"
            size="large"
            class="select-plan"
            @change="handlePlanChange"
            v-if="props.showSearch"
            :get-popup-container="getPopupContainer"
          >
            <a-select-option v-for="plan in planList" :key="plan.id" :value="plan.id">
              {{ plan.planName }}
            </a-select-option>
          </a-select>
          <a-button type="link" class="configure-button" @click="configurePlan"> 配置监控方案 </a-button>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="less">
  /* 顶部外框 */
  .public-wrapper {
    display: flex;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 20px;
    background-color: #fff;
    // height: 100vh; /* 设置固定高度 */
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  }

  /* 顶部栏 */
  .top-bar {
    display: flex;
    align-items: center;
    width: 100%;
  }

  .game-info {
    display: flex;
    align-items: center;
    gap: 10px;
  }

  .logo {
    width: 70px;
    height: 70px;
    border-radius: 10px;
  }

  .game-details {
    display: flex;
    flex-direction: column;
  }

  .game-title {
    font-size: 20px;
    font-weight: bold;
    margin: 0;
  }

  .game-subtitle {
    font-size: 14px;
    color: gray;
    margin: 0;
  }
  /* 竖线样式 */
  .vertical-line {
    width: 1px;
    height: 80px;
    background-color: #ccc;
    margin-left: 50px;
    margin-right: 50px;
  }

  /* 搜索栏 */
  .search-bar {
    flex: 1;
    width: 500px;
    margin: 0 20px;
  }

  .search-container {
    position: relative;
    display: flex;
    align-items: center;
  }

  .search-icon {
    position: absolute;
    left: 10px;
    color: #888;
    font-size: 16px;
  }

  .search-input .ant-select-selector {
    //padding: 8px 8px 8px 40px;
    //border: 1px solid #ccc;
    //border-radius: 50px;
    //width: 100%;
    //font-size: 16px;
    //background-color: #f1f3f5;

    height: 40px !important; /* 控制输入框高度 */
    line-height: 40px !important;
    border-radius: 50px;
  }

  /* 方案栏 */
  .prog-bar {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-left: auto;
    width: 18vw;
  }

  .select-plan {
    padding: 8px;
    border-radius: 5px;
    width: 200px;
  }

  /* 配置监控方案按钮样式 */
  .configure-button {
    color: #018ffb; /* 蓝色 */
    font-size: 14px;
    padding: 0; /* 去掉默认内边距，保持文字按钮风格 */
    background: transparent; /* 透明背景 */
    border: none; /* 去掉边框 */
    cursor: pointer;
    margin-left: 10px; /* 与选择框保持一定距离 */
  }

  .configure-button:hover {
    color: #0170c9; /* 悬停时颜色变深 */
    text-decoration: underline; /* 悬停时添加下划线 */
  }

  /* 在原有样式基础上添加 */
  :deep(.ant-picker-dropdown),
  :deep(.ant-select-dropdown) {
    position: absolute !important;
    z-index: 1050 !important;
    transform-origin: 0 0;
  }
</style>
