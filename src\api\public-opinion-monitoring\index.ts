import { defHttp } from '/@/utils/http/axios';
enum Api {
  crawlerForm = '/crawler/crawlerForm',
  getGameInfo = '/appInfo/queryByName',
  getGameByPageInfo = '/game/getAllGameByPage',
  findGamesByPrefix = '/trie/findGames',

  getPlanTableListByUserId = '/plan/getPlanTable',
  getRealTimePublicOpinionQueryParameters = '/realTimePublicOpinion/queryChannelsParameters',
  getRealTimePublicOpinionQueryPlansParameters = '/realTimePublicOpinion/queryPlansParameters',
  getRealTimePublicOpinionShow = '/realTimePublicOpinion/show',

  getPublicOpinionData = 'dorisCrawlPageContent/getPublicOpinionData',
  getHotData = 'tokenCount/getHotData',
  getTrendingData = 'tokenCount/getTrendingData',
}

/**
 * @description: 配置监控方案-新增
 */
export function crawlerFormApi(params) {
  return defHttp.post({
    url: Api.crawlerForm,
    params,
  });
}
/**
 * @description: 检索游戏-获取游戏信息
 */
export function getGameInfoApi(params) {
  return defHttp.get({
    url: Api.getGameInfo,
    params,
  });
}
/**
 * @description: 检索游戏-获取默认游戏信息
 */
export function getGameByPageInfo(params) {
  return defHttp.get({
    url: Api.getGameByPageInfo,
    params,
  });
}

/**
 * @description: 前缀树查询游戏-根据输入的文字，获取全部前缀符合的游戏
 */
export function findGamesByPrefixApi(params: { prefix: string }) {
  return defHttp.get({
    url: Api.findGamesByPrefix,
    params,
  });
}
/**
 * @description: 获取用户监控方案列表
 */
export function getPlanTableListByUserIdApi(params: { userId: string; gameId: string }) {
  return defHttp.get({
    url: Api.getPlanTableListByUserId,
    params,
  });
}
/**
 * @description: 获取实时舆情下拉框数据 父渠道/子渠道/渠道名称
 */
export function getRealTimePublicOpinionQueryParameters(params: { userId: string; gameId: string }) {
  return defHttp.get({
    url: Api.getRealTimePublicOpinionQueryParameters,
    params,
  });
}

/**
 * @description: 获取实时舆情下拉框数据 方案-渠道名称
 */
export function getRealTimePublicOpinionQueryPlansParameters(params: { userId: string; gameId: string }) {
  return defHttp.get({
    url: Api.getRealTimePublicOpinionQueryPlansParameters,
    params,
  });
}

/**
 * @description: 获取实时舆情数据
 */
export function getRealTimePublicOpinionShow(params: { userId: string; gameId: string }) {
  return defHttp.post({
    url: Api.getRealTimePublicOpinionShow,
    params,
  });
}

// 舆情数据及周环比日环比获取
export function getPublicOpinionDataApi(params) {
  return defHttp.get({
    url: Api.getPublicOpinionData,
    params,
  });
}

// 获取热点数据
export function getHotDataApi(params) {
  return defHttp.post({
    url: Api.getHotData,
    data: params,
  });
}

// 获取热门话题
export function getTrendingDataApi(params) {
  return defHttp.post({
    url: Api.getTrendingData,
    data: params,
  });
}
