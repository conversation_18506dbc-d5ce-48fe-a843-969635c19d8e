import { defHttp } from '/@/utils/http/axios';

enum Api {
  focusPublisher = '/shop/focusPublisher',
  unFocusPublisher = '/shop/unFocusPublisher',
  focusPublisherList = '/shop/focusPublisherList',
  getAllPublisher = '/shop/getAllPublisher',
  getAllGenre = '/shop/getAllGenre',
  getRankByType = '/shop/getRankByType',
}

/**
 * 关注发行商
 * @param publisherName 发行商名称
 */
export function focusPublisherApi(publisherName: string) {
  return defHttp.post({
    url: Api.focusPublisher,
    params: { name: publisherName },
  });
}

/**
 * 取消关注发行商
 * @param publisherName 发行商名称
 */
export function unfocusPublisherApi(publisherName: string) {
  return defHttp.post({
    url: Api.unFocusPublisher,
    params: { name: publisherName },
  });
}

/**
 * 获取我的发行商列表
 * @param params 查询参数 (可选)
 */
export function getMyPublishersApi(params?: {
  startDate?: string;
  endDate?: string;
  platform?: string[];
  country?: string[];
  genre?: string[];
}) {
  return defHttp.get({
    url: Api.focusPublisherList,
    params,
  });
}

/**
 * 获取所有发行商（分页）
 * @param pageNo 页码
 * @param pageSize 每页数量
 * @param publisher 搜索关键词
 */
export function getAllPublishersApi(pageNo: number, pageSize: number, publisher: string = '') {
  return defHttp.get({
    url: Api.getAllPublisher,
    params: {
      pageNo,
      pageSize,
      publisher,
    },
  });
}

/**
 * 获取所有游戏类别
 */
export function getAllGameGenresApi() {
  return defHttp.get({
    url: Api.getAllGenre,
  });
}

/**
 * 获取发行商游戏排名数据
 * @param publisherId 发行商名称
 * @param countryName 国家名称
 */
export function getPublisherRankApi(publisherId: string, countryName: string) {
  return defHttp.get({
    url: Api.getRankByType,
    params: { 
      publisherId, 
      countryName 
    },
  });
}