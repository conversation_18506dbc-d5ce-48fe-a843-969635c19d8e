// publisherApi.ts
import { defHttp } from '/@/utils/http/axios';

enum Api {
  GetPublisherGameInfoDetail = '/shop/getPublisherGameInfoDetail',
  GetPublisherInfoDetail = '/shop/getPublisherInfoDetail',
  GetPublisherInfo7Detail = '/shop/getPublisherInfo7Detail',
  FocusPublisher = '/shop/focusPublisher',
  UnFocusPublisher = '/shop/unFocusPublisher',
  QueryByPublisherName = '/shop/queryByPublisherName',
  GetAllGenre = '/shop/getAllGenre',
}

/**
 * 获取发行商基本信息
 * @param publisherName 发行商名称
 */
export function getPublisherGameInfoDetailApi(publisherName: string) {
  return defHttp.get({
    url: Api.GetPublisherGameInfoDetail,
    params: { publisherName },
  });
}

/**
 * 获取发行商游戏信息详情
 * @param publisherName 发行商名称
 */
export function getPublisherInfoDetailApi(publisherName: string) {
  return defHttp.get({
    url: Api.GetPublisherInfoDetail,
    params: { publisherName },
  });
}

/**
 * 获取发行商近7天趋势数据
 * @param params 查询参数
 */
export function getPublisherInfo7DetailApi(params: {
  countryName: string[];
  deviceName: string[];
  gameCategory: string[];
  isPaid: string[];
  startTime: string;
  endTime: string;
  publisherId: string;
}) {
  return defHttp.get({
    url: Api.GetPublisherInfo7Detail,
    params,
  });
}

/**
 * 关注发行商
 * @param publisherName 发行商名称
 */
export function focusPublisherApi(publisherName: string) {
  return defHttp.post({
    url: Api.FocusPublisher,
    params: { name: publisherName },
  });
}

/**
 * 取消关注发行商
 * @param publisherName 发行商名称
 */
export function unfocusPublisherApi(publisherName: string) {
  return defHttp.post({
    url: Api.UnFocusPublisher,
    params: { name: publisherName },
  });
}

/**
 * 查询是否关注了该发行商
 * @param publisherName 发行商名称
 */
export function queryFocusStatusApi(publisherName: string) {
  return defHttp.get({
    url: Api.QueryByPublisherName,
    params: { publisherName },
  });
}

/**
 * 获取所有游戏类别
 */
export function getAllGenreApi() {
  return defHttp.get({
    url: Api.GetAllGenre,
  });
}