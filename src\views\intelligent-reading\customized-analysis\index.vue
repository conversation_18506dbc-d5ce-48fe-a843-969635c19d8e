<template>
  <div class="customized-analysis">
    <div class="main-content">
      <div class="tab-header">
        <div class="tab-left">
          <span class="tab-title">数据报告（2）</span>
          <a-button type="text" class="edit-btn" title="编辑">
            <EditOutlined />
          </a-button>
        </div>
        <a-button type="primary" size="small" class="download-btn">
          <template #icon>
            <DownloadOutlined />
          </template>
          <span class="download-text" >下载</span>
        </a-button>
      </div>
      <div class="report-card">
        <div class="report-header">
          <h2>2025 年第一季度Minecraft: Dream it, Build it!用户数据分析</h2>
        </div>
        <div class="report-desc">
          2025 年第一季度，用户留存表现方面，Minecraft: Dream it, Build it!游戏用户每月呈现递减趋势，其中 1 月份留存4245，2 月份留存4069，3
          月份留存3059
        </div>
        <LineChart :chartData="lineChartData" :height="'400px'" :colorList="['#38bdf8']" style="margin-bottom: 24px" />
        <div class="report-header">
          <h2>2025 年第一季度App Store用户数据分析</h2>
        </div>
        <div class="report-desc">
          2025 年第一季度，在马来西亚地区，付费意愿用户总量保持稳定。<br />
          但是吉兰丹的付费意愿用户总数明显提升，从第5位升至第3位，主要集中在15-20岁男性用户，该州氪金游戏的前三名为Mobile Legends: Bang
          Bang、eFootball、Last War:Survival
        </div>
        <Bar :chartData="barChartData" :height="'350px'" :seriesColor="barColors" style="margin-bottom: 8px" />
      </div>

      <div class="footer-tips">还有疑问？请咨询云舟管家：13X****XXXX</div>
    </div>
    <div class="side-panel">
      <div class="side-title"> <span class="side-bar"></span>历史生成报告 </div>
      <div class="history-list">
        <div class="history-item">
          <a-button type="text" class="history-delete-btn">
            <DeleteOutlined />
          </a-button>
          <div class="history-header">
            <span>数据报告（2）</span>
          </div>
          <div class="history-desc">2025年不同地区用户在App Store上的消费金额</div>
        </div>
        <div class="history-item">
          <a-button type="text" class="history-delete-btn">
            <DeleteOutlined />
          </a-button>
          <div class="history-header">
            <span>数据报告（1）</span>
            <a-button type="link" size="small" class="downloaded-btn">已下载</a-button>
          </div>
          <div class="history-desc">2025年不同地区用户在App Store上的消费金额</div>
        </div>
      </div>
      <div class="side-footer">
        <div class="create-btn-wrapper">
          <a-button type="primary" class="create-btn-beauty" block @click="openReportModal">
            <PlusCircleOutlined style="font-size: 22px; margin-right: 8px; vertical-align: -3px" />
            新建报告
          </a-button>
        </div>
      </div>
    </div>
    
    <a-modal
      v-model:visible="showReportModal"
      :footer="null"
      width="700px"
      centered
      wrapClassName="custom-report-modal-new"
      :closable="false"
      @cancel="closeReportModal"
    >
      <div class="modal-close-btn" @click="closeReportModal">
        <CloseOutlined />
      </div>
      
      <div class="modal-steps-header">
        <div class="steps-container">
          <div 
            v-for="(step, index) in steps" 
            :key="index" 
            class="step-item"
            :class="{ 
              'step-active': index === currentStep,
              'step-completed': index < currentStep 
            }"
          >
            <div class="step-circle">
              <span v-if="index < currentStep">✓</span>
              <span v-else>{{ index + 1 }}</span>
            </div>
            <div class="step-title">{{ step.title }}</div>
          </div>
        </div>
      </div>

      <div class="modal-content-new">
        <div v-if="currentStep === 0" class="step-content">
          <div class="product-selection-horizontal">
            <div class="search-section left-search">
              <a-select
                show-search
                allow-clear
                v-model:value="gameSearchValue"
                :placeholder="currentSelectType === 'main' ? '请选择本品' : '请选择竞品'"
                :filter-option="false"
                :not-found-content="gameSearchLoading ? '加载中...' : '无数据'"
                class="game-search-input"
                size="large"
                style="width: 220px"
                @search="handleGameSearch"
                @change="onGameSelect"
                @clear="handleGameClear"
                @popup-scroll="handleGameScroll"
                @focus="handleGameFocus"
              >
                <a-select-option v-for="item in gameOptions" :key="item.id" :value="item.id">
                  <div style="display: flex; align-items: center">
                    <img :src="item.iconUrl" style="width: 24px; height: 24px; margin-right: 8px" v-if="item.iconUrl" />
                    <span>{{ item.nameZh }}</span>
                  </div>
                </a-select-option>
                <a-select-option v-if="gameSearchLoading" key="loading" disabled>
                  <a-spin size="small" />
                </a-select-option>
                <a-select-option v-if="hasGameMore" key="more" disabled> 滚动加载更多... </a-select-option>
              </a-select>
              </div>
            <div class="product-panel right-panel">
              <div class="product-group">
                <div class="group-title">本品</div>
                <div class="product-item-row" :class="{ 'card-selecting': currentSelectType === 'main' }" @click="selectType('main')">
                  <a-avatar v-if="mainProduct" :src="mainProduct.iconUrl" size="40" class="product-avatar" />
                  <a-avatar v-else size="40" class="product-avatar placeholder-avatar"><UserOutlined /></a-avatar>
                  <span class="product-name">{{ mainProduct ? mainProduct.nameZh : '（待选择）' }}</span>
                </div>
              </div>
              <div class="product-group">
                <div class="group-title">竞品</div>
                <div v-if="competitorProducts.length">
                  <div v-for="(item, idx) in competitorProducts" :key="item.id" class="product-item-row" :class="{ 'card-selecting': currentSelectType === 'competitor' }" @click="selectType('competitor')">
                    <a-avatar :src="item.iconUrl" size="40" class="product-avatar" />
                    <span class="product-name">{{ item.nameZh }}</span>
                    <a-button type="text" size="small" @click.stop="removeCompetitor(idx)" style="color:#f56c6c;">
                      <CloseOutlined />
                    </a-button>
                  </div>
                </div>
                <div v-else class="product-item-row empty" :class="{ 'card-selecting': currentSelectType === 'competitor' }" @click="selectType('competitor')">
                  <a-avatar size="40" class="product-avatar placeholder-avatar"><UserOutlined /></a-avatar>
                  <span class="product-name placeholder-name">（待选择）</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div v-else-if="currentStep === 1" class="step-content">
          <div class="dimension-list"> <div class="dimension-section" v-for="(row, rowIndex) in dimensionConfig" :key="'dim-row-' + rowIndex">
              <div class="dimension-row-single">
                <div class="dimension-label-single">
                  <span class="dimension-label-bar"></span>
                  {{ row.label }}
                </div>
                <div class="dimension-checkboxes-group">
                  <div class="dimension-checkboxes-item" v-for="(option, colIndex) in row.options" :key="'cb-'+ rowIndex + '-' + colIndex">
                    <a-checkbox v-model:checked="dimensionChecked[rowIndex][colIndex]">
                      {{ option }}
                    </a-checkbox>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div v-else-if="currentStep === 2" class="step-content">
          <div class="dimension-list"> <div class="dimension-section"> <div class="dimension-row-single">
                <div class="dimension-label-single">
                  <span class="dimension-label-bar"></span>
                  时间维度
                </div>
                <div class="dimension-checkboxes-group">
                  <div class="dimension-checkboxes-item" v-for="(option, index) in timeDimensionOptions" :key="'time-cb-' + index">
                    <a-checkbox v-model:checked="timeDimensionChecked[index]">
                      {{ option }}
                    </a-checkbox>
                  </div>
                </div>
              </div>
            </div>
            <div class="date-range-picker-section">
                <a-range-picker v-model:value="dateRange" />
            </div>
          </div>
        </div>
        
        <div v-else-if="currentStep === 3" class="step-content">
          <div class="processing-content">
            <div class="report-preview-header-input">
              <a-input 
                v-model:value="reportName" 
                placeholder="请输入报告名称" 
                class="report-name-input"
              />
            </div>

            <div class="selected-items-grid">
              <div class="selected-item-group">
                <div class="group-label">已选竞品</div>
                <div class="group-tags">
                  <div class="selected-tag" v-for="(item, idx) in competitorProducts" :key="item.id">
                    <img :src="item.iconUrl" class="tag-icon" v-if="item.iconUrl" />
                    <span>{{ item.nameZh }}</span>
                    <CloseOutlined class="tag-close-icon" @click="removeCompetitor(idx)" />
                  </div>
                </div>
              </div>

              <div class="selected-item-group">
                <div class="group-label">已选数据</div>
                <div class="group-tags">
                  <div class="selected-tag" v-for="(item, idx) in selectedData" :key="'data-'+idx">
                    <span>{{ item }}</span>
                    <CloseOutlined class="tag-close-icon" @click="removeSelectedData(idx)" />
                  </div>
                </div>
              </div>

              <div class="selected-item-group">
                <div class="group-label">时间维度</div>
                <div class="group-tags">
                  <div class="selected-tag" v-if="selectedTimeDimension">
                    <span>{{ selectedTimeDimension }}</span>
                    <CloseOutlined class="tag-close-icon" @click="removeSelectedTimeDimension" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="modal-footer-new">
        <a-button 
          v-if="currentStep > 0" 
          class="prev-btn"
          @click="prevStep"
        >
          上一步
        </a-button>
        <a-button 
          type="primary" 
          class="next-btn"
          @click="nextStep"
        >
          {{ currentStep < 3 ? '下一步' : '生成报告' }} 
        </a-button>
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import * as echarts from 'echarts';
import { DownloadOutlined, DeleteOutlined, PlusCircleOutlined, EditOutlined, CloseOutlined, UserOutlined, SearchOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import LineChart from '@/views/public-opinion-monitoring/components/chart/LineChart.vue';
import Bar from '@/components/chart/Bar.vue';
import { getGameInfoApi } from '@/api/public-opinion-monitoring';
import { debounce } from 'lodash-es';
import { message } from 'ant-design-vue'; // 确保导入 message
import Dayjs from 'dayjs'; // 导入 Dayjs

// 新增：本品、竞品、当前选择类型
const mainProduct = ref(null); // { label, icon, value, raw }
const competitorProducts = ref([]); // 多个竞品
const currentSelectType = ref('main'); // 'main' or 'competitor'

const barChartData = [
  { name: '柔佛', value: 1400 },
  { name: '吉打', value: 1600 },
  { name: '吉兰丹', value: 1700 },
  { name: '马六甲', value: 1300 },
  { name: '森美兰', value: 2000 },
  { name: '彭亨', value: 1500 },
  { name: '槟城', value: 1600 },
];

const barColors = [
  "#22d3ee", // 柔佛
  "#c4b5fd", // 吉打
  "#60a5fa", // 吉兰丹
  "#fdba74", // 马六甲
  "#f9a8d4", // 森美兰
  "#a78bfa", // 彭亨
  "#fcd34d", // 槟城
];

const lineChartData = {
  xAxisData: ['2025-01', '2025-02', '2025-03'],
  seriesData: [
    {
      name: '留存人数',
      data: [4245, 4069, 3059],
      areaStyle: { color: '#e0f2fe' },
      lineStyle: { color: '#38bdf8' },
      itemStyle: { color: '#38bdf8' },
    },
  ],
  legendData: ['留存人数'],
  yAxisName: '',
};

const showReportModal = ref(false);
const currentStep = ref(0);
const gameSearchValue = ref('');
const gameSearchLoading = ref(false);
const gameOptions = ref([]);
const hasGameMore = ref(true);
const currentPage = ref(1);
const pageSize = 20;

// 步骤定义
const steps = [
  { title: '选择竞品' },
  { title: '数据维度' },
  { title: '时间维度' },
  { title: '正在处理' } // 标题保持不变，因为是处理页面
];

// 数据维度配置，每行一个对象
const dimensionConfig = ref([
  {
    label: '商店信息',
    options: ['实时排名', '评分趋势', '每日评分趋势', '评论详情']
  },
  {
    label: '用户数据',
    options: ['用户总量', '活跃用户', '新增用户', '付费用户']
  },
  {
    label: '设备信息',
    options: ['操作系统', '设备型号', '网络类型']
  },
  {
    label: '留存分析',
    options: ['次日留存', '7日留存', '30日留存']
  }
]);

// 数据维度选中状态，二维数组，与 dimensionConfig 结构对应
const dimensionChecked = ref([]);

function initializeDimensionChecked() {
  dimensionChecked.value = dimensionConfig.value.map(row => {
    // 假设第一行默认选中前三项，其他行默认都不选中
    if (row.label === '商店信息') {
      return Array(row.options.length).fill(false).map((_, index) => index < 3);
    }
    return Array(row.options.length).fill(false);
  });
}

// 时间维度配置
const timeDimensionOptions = ref(['按日', '按周', '按月']);
const timeDimensionChecked = ref(Array(timeDimensionOptions.value.length).fill(false)); // 默认都不选中

// 日期范围选择器
const dateRange = ref([]); // 存储日期范围，例如 [Dayjs, Dayjs]

// 新增：第四步展示的数据
const reportName = ref(''); // 报告名称
const selectedData = ref([]); // 已选数据，例如 ['下载']
const selectedTimeDimension = ref(''); // 已选时间维度，例如 '最近一个月'


// 在组件挂载时初始化选中状态
onMounted(() => {
  initializeDimensionChecked();
  // 假设时间维度默认选中“按月”
  timeDimensionChecked.value[2] = true; 
});


function openReportModal() {
  showReportModal.value = true;
  currentStep.value = 0;
  // 打开弹窗时重置选择
  mainProduct.value = null;
  competitorProducts.value = [];
  currentSelectType.value = 'main';
  gameSearchValue.value = '';
  gameOptions.value = [];
  currentPage.value = 1;
  hasGameMore.value = true;
  // 重置数据维度选中状态
  initializeDimensionChecked(); // 调用初始化函数重置
  // 重置时间维度选中状态
  timeDimensionChecked.value = Array(timeDimensionOptions.value.length).fill(false);
  timeDimensionChecked.value[2] = true; // 默认选中“按月”
  dateRange.value = []; // 清空日期范围

  // 初始化第四步的报告名称为默认值
  const now = Dayjs();
  reportName.value = `我的报告 ${now.format('YYYYMMDD_HHmmss')}`; // 提供一个默认且唯一的名称

  // 假设“下载”是固定显示的已选数据，实际应根据业务逻辑填充
  selectedData.value = ['下载']; 

  // 根据 timeDimensionChecked 和 dateRange 来设置 selectedTimeDimension
  if (timeDimensionChecked.value[0]) { // 按日
    selectedTimeDimension.value = '按日';
  } else if (timeDimensionChecked.value[1]) { // 按周
    selectedTimeDimension.value = '按周';
  } else if (timeDimensionChecked.value[2]) { // 按月
    selectedTimeDimension.value = '最近一个月'; // 假设默认是最近一个月
  }
  // 如果选择了日期范围，则覆盖之前的按日/周/月
  if (dateRange.value && dateRange.value.length === 2 && dateRange.value[0] && dateRange.value[1]) {
    selectedTimeDimension.value = `${dateRange.value[0].format('YYYY/MM/DD')} - ${dateRange.value[1].format('YYYY/MM/DD')}`;
  }
}

function closeReportModal() {
  showReportModal.value = false;
}

function nextStep() {
  if (currentStep.value === 0) {
    if (!mainProduct.value) {
      message.warning('请选择本品');
      return;
    }
    if (!competitorProducts.value || competitorProducts.value.length === 0) {
      message.warning('请至少选择一个竞品');
      return;
    }
  }
  if (currentStep.value === 1) {
    // 检查每一行是否至少勾选一个数据维度
    let atLeastOneChecked = false;
    for (let i = 0; i < dimensionChecked.value.length; i++) {
      if (dimensionChecked.value[i].some(Boolean)) {
        atLeastOneChecked = true;
        break;
      }
    }
    if (!atLeastOneChecked) {
      message.warning('请至少选择一个数据维度');
      return;
    }
  }
  if (currentStep.value === 2) {
    // 检查时间维度是否至少勾选一个
    if (!timeDimensionChecked.value.some(Boolean)) {
      message.warning('请至少选择一个时间维度');
      return;
    }
    // 假设如果选择了“按日”或“按周”，需要选择日期范围
    if (timeDimensionChecked.value[0] || timeDimensionChecked.value[1]) { // 如果选择了按日或按周
        if (!dateRange.value || dateRange.value.length !== 2 || !dateRange.value[0] || !dateRange.value[1]) {
            message.warning('请选择日期范围');
            return;
        }
    }
    // 当从第三步进入第四步时，更新第四步展示的数据 (报告名称除外，因为它现在是用户输入)
    // reportName.value 在 openReportModal 时已设置默认值
    selectedData.value = ['下载']; // 假设所有报告都涉及“下载”数据，你可以根据 dimensionChecked 来动态生成
    
    // 根据当前时间维度的选择来设置 selectedTimeDimension
    if (timeDimensionChecked.value[0]) {
      selectedTimeDimension.value = '按日';
    } else if (timeDimensionChecked.value[1]) {
      selectedTimeDimension.value = '按周';
    } else if (timeDimensionChecked.value[2]) {
      selectedTimeDimension.value = '最近一个月';
    }

    if (dateRange.value && dateRange.value.length === 2 && dateRange.value[0] && dateRange.value[1]) {
      selectedTimeDimension.value = `${dateRange.value[0].format('YYYY/MM/DD')} - ${dateRange.value[1].format('YYYY/MM/DD')}`;
    }
  }

  if (currentStep.value < 3) currentStep.value += 1;
  else {
    // 在这里执行生成报告的逻辑
    if (!reportName.value.trim()) {
      message.warning('报告名称不能为空');
      return;
    }
    message.success('报告生成中...');
    closeReportModal();
  }
}
function prevStep() {
  if (currentStep.value > 0) currentStep.value -= 1;
}

// 搜索加载更多
const handleGameScroll = (e) => {
  const { target } = e;
  const { scrollTop, scrollHeight, clientHeight } = target;
  const isBottom = scrollTop + clientHeight >= scrollHeight - 100;
  if (isBottom && !gameSearchLoading.value && hasGameMore.value) {
    currentPage.value++;
    loadGameOptions();
  }
};

const loadGameOptions = async () => {
  if (gameSearchLoading.value) return;
  gameSearchLoading.value = true;
  try {
    const res = await getGameInfoApi({
      pageNo: currentPage.value,
      pageSize: pageSize,
      platformName: gameSearchValue.value,
    });
    const data = (res.records || []).map(item => ({
      id: item.id,
      label: item.nameZh,
      value: item.id,
      icon: item.iconUrl,
      nameZh: item.nameZh,
      iconUrl: item.iconUrl,
      raw: item,
    }));
    if (currentPage.value === 1) {
      gameOptions.value = data;
    } else {
      gameOptions.value = [...gameOptions.value, ...data];
    }
    if (!res.records || res.records.length < pageSize) {
      hasGameMore.value = false;
    }
  } finally {
    gameSearchLoading.value = false;
  }
};

// 修改 debounce 的使用方式
const handleGameSearch = debounce((value) => {
  gameSearchValue.value = value;
  currentPage.value = 1;
  hasGameMore.value = true;
  gameOptions.value = []; // 每次新搜索清空旧数据
  loadGameOptions();
}, 400); // 确保 debounce 完整包裹了匿名函数

const handleGameClear = () => {
  gameSearchValue.value = '';
  gameOptions.value = [];
  currentPage.value = 1;
  hasGameMore.value = true;
};


function selectType(type) {
  currentSelectType.value = type;
  gameSearchValue.value = ''; // 切换选择类型时清空搜索框
  gameOptions.value = []; // 清空之前的搜索结果
  currentPage.value = 1;
  hasGameMore.value = true;
}

function onGameSelect(value, option) {
  const selected = gameOptions.value.find(item => item.id === value);
  if (currentSelectType.value === 'main') {
    mainProduct.value = selected;
  } else {
    if (selected && !competitorProducts.value.some(item => item.id === selected.id)) {
      if (competitorProducts.value.length >= 3) { // 假设最多选3个竞品
        message.warning('最多只能选择3个竞品');
        return;
      }
      competitorProducts.value.push(selected);
    }
  }
  gameSearchValue.value = ''; // 选中后清空搜索框
  gameOptions.value = []; // 清空搜索结果，防止重复选择
}

// 搜索框聚焦时加载数据
const handleGameFocus = () => {
  if (!gameOptions.value.length && gameSearchValue.value === '') { // 只有在没有选项且搜索框为空时才加载
    currentPage.value = 1;
    hasGameMore.value = true;
    gameOptions.value = [];
    loadGameOptions();
  }
};

// 删除竞品
function removeCompetitor(idx) {
  competitorProducts.value.splice(idx, 1);
}

// 新增：删除已选数据（如果需要交互）
const removeSelectedData = (index) => {
  selectedData.value.splice(index, 1);
};

// 新增：删除已选时间维度（如果需要交互）
const removeSelectedTimeDimension = () => {
  selectedTimeDimension.value = '';
};
</script>

<style scoped>
/* 全局样式：确保html和body占据100%高度并隐藏溢出 */
html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden; /* 防止整个页面滚动 */
}

.customized-analysis {
  display: flex;
  height: 100vh; /* 使用视口高度 */
  padding: 16px;
  background: #f5f8fa;
  overflow: hidden; /* 防止customized-analysis自身滚动 */
  box-sizing: border-box;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin-right: 24px;
  overflow: hidden; /* 防止main-content自身滚动 */
  min-height: 0; /* 重要：允许flex子元素正确收缩 */
}

.tab-header {
  display: flex;
  align-items: center;
  min-width: 0;
  margin-bottom: 16px;
  flex-shrink: 0; /* 防止此元素在空间不足时收缩 */
}
.tab-left {
  display: flex;
  align-items: center;
  gap: 2px;
  min-width: 0;
}
.tab-title {
  font-size: 18px;
  font-weight: bold;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}
.edit-btn {
  margin-left: 2px;
  color: #666;
  font-size: 18px;
  padding: 0 4px;
  border: none;
  background: transparent;
  box-shadow: none;
  transition: color 0.2s;
}
.download-btn {
  margin-left: auto;
}
.download-btn .download-text {
  display: inline;
}

.report-card {
  background: #fff;
  border-radius: 12px;
  padding: 32px 32px 16px 32px;
  box-shadow: 0 2px 8px 0 #f0f1f2;
  flex: 1; /* 填充剩余空间 */
  min-height: 0; /* 重要：允许flex子元素正确收缩 */
  overflow-y: auto; /* 只允许report-card内部滚动 */
  -webkit-overflow-scrolling: touch;
}

.footer-tips {
  color: #bbb;
  font-size: 13px;
  text-align: right;
  margin-top: 16px;
  margin-right: 10px;
  flex-shrink: 0; /* 防止此元素在空间不足时收缩 */
}

.report-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 8px;
}
.report-header h2 {
  font-size: 2rem;
  font-weight: bold;
  margin: 0;
}
.report-desc {
  color: #555;
  margin-bottom: 16px;
  font-size: 15px;
}

.side-panel {
  width: 340px;
  background: #fff;
  border-radius: 18px;
  padding: 24px 20px 0 20px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 24px 0 #fff;
  height: calc(100vh - 32px); /* 减去外层padding */
  flex-shrink: 0;
  box-sizing: border-box;
  min-height: 0; /* 重要：允许flex子元素正确收缩 */
}

.side-title {
  font-weight: bold;
  margin-bottom: 16px;
  font-size: 1.1rem;
  color: #222;
  display: flex;
  align-items: center;
  flex-shrink: 0;
}
.side-bar {
  width: 4px;
  height: 20px;
  background: #2d5be3;
  border-radius: 2px;
  margin-right: 8px;
}
.history-list {
  flex: 1;
  overflow-y: auto; /* 只允许history-list内部滚动 */
  -webkit-overflow-scrolling: touch;
  min-height: 0; /* 重要：允许flex子元素正确收缩 */
}
.history-item {
  background: #f7faff;
  border-radius: 10px;
  padding: 14px 16px 10px 16px;
  margin-bottom: 18px;
  position: relative;
  box-shadow: 0 2px 8px 0 #f0f1f2;
  transition: box-shadow 0.2s;
}
.history-item:hover {
  box-shadow: 0 4px 16px 0 #e6eaf1;
}
.history-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  font-size: 15px;
  margin-bottom: 2px;
}
.downloaded-btn {
  color: #2d5be3;
  background: #e6f7ff;
  border: none;
  border-radius: 4px;
  padding: 0 8px;
  font-size: 13px;
  height: 22px;
  line-height: 22px;
  margin-left: 6px;
}
.history-desc {
  color: #555;
  font-size: 14px;
  margin-left: 2px;
  word-break: break-all;
}
.history-delete-btn {
  position: absolute;
  right: 10px;
  top: 10px;
  color: #bfbfbf;
  font-size: 18px;
  z-index: 2;
  padding: 0;
  background: transparent;
  border: none;
  box-shadow: none;
  transition: color 0.2s;
}
.history-delete-btn:hover {
  color: #ff4d4f;
  background: transparent;
}
.side-footer {
  background: #fff;
  padding-bottom: 24px;
  padding-top: 18px;
  z-index: 10;
  border-bottom-left-radius: 18px;
  border-bottom-right-radius: 18px;
  box-shadow: 0 4px 24px 0 #fff;
  flex-shrink: 0;
}
.create-btn-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
}
.create-btn-beauty {
  background: #2d5be3;
  color: #fff;
  border: none;
  border-radius: 16px;
  padding: 0 0;
  font-size: 1.18rem;
  cursor: pointer;
  width: 90%;
  height: 48px;
  font-weight: 500;
  letter-spacing: 2px;
  box-shadow: 0 2px 8px 0 #e6eaf1;
  transition: background 0.2s, box-shadow 0.2s;
  margin: 0 auto;
  display: flex;
  align-items: center;
  justify-content: center;
}
.create-btn-beauty:hover {
  background: #1746a2;
  box-shadow: 0 6px 18px 0 #dbeafe;
}

/* 新的弹窗样式 */
.custom-report-modal-new :deep(.ant-modal-content) {
  border-radius: 20px;
  padding: 0;
  background: #fff;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.custom-report-modal-new :deep(.ant-modal-close) {
  display: none;
}

.modal-close-btn {
  position: absolute;
  top: 16px;
  right: 20px;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
  color: #666;
  font-size: 14px;
  transition: all 0.2s;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.modal-close-btn:hover {
  background: #fff;
  color: #333;
  transform: scale(1.1);
}

.modal-steps-header {
  background: linear-gradient(135deg, #60a5fa 0%, #0ba2e3 100%); /* 蓝色系渐变 */
  padding: 40px 0 30px 0;
  position: relative;
}

.steps-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 60px; /* 调整步骤间距 */
  position: relative;
}

.step-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.step-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3); /* 未激活/未完成状态的背景 */
  border: 2px solid rgba(255, 255, 255, 0.5); /* 未激活/未完成状态的边框 */
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff; /* 未激活/未完成状态的数字颜色 */
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 8px;
  transition: all 0.3s;
}

.step-item.step-active .step-circle {
  background: #fff; /* 激活状态背景 */
  color: #0ba2e3; /* 激活状态数字颜色 */
  border-color: #fff;
  box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
}

.step-item.step-completed .step-circle {
  background: rgba(255, 255, 255, 0.9); /* 完成状态背景 */
  color: #0ba2e3; /* 完成状态数字颜色 */
  border-color: rgba(255, 255, 255, 0.9);
}

.step-title {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
  text-align: center;
}

.step-item.step-active .step-title {
  color: #fff;
  font-weight: 600;
}

.modal-content-new {
  padding: 40px;
  min-height: 320px; /* 保证内容区域有足够高度 */
}

.step-content {
  width: 100%;
  /* 确保step-content内部的内容居中 */
  display: flex;
  flex-direction: column;
  align-items: center;
}

.product-selection-horizontal {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;
  gap: 36px;
}

.left-search {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  justify-content: flex-start;
  min-width: 220px;
  margin-top: 8px;
}

.right-panel {
  min-width: 320px;
  max-width: 360px;
  width: 100%;
}

.product-panel {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px 0 #f0f1f2;
  padding: 24px 24px 16px 24px;
  min-width: 320px;
  max-width: 360px;
  width: 100%;
  margin: 0 auto;
}

.product-group {
  margin-bottom: 18px;
}

.group-title {
  font-size: 14px;
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
}

.product-item-row {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8fafc;
  border-radius: 8px;
  padding: 8px 12px;
  margin-bottom: 8px;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border 0.2s, box-shadow 0.2s;
}

.product-item-row.card-selecting {
  border: 2px solid #a78bfa !important;
  box-shadow: 0 0 0 2px #ede9fe;
}

.product-item-row.empty {
  background: #fafafa;
  color: #aaa;
}

.product-avatar {
  flex-shrink: 0;
}

.product-name {
  font-size: 15px;
  color: #333;
  flex: 1;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.product-name.placeholder-name {
  color: #9ca3af;
}

/* Ant Design Select Input 样式覆盖 */
.game-search-input :deep(.ant-select-selector) {
  height: 48px !important;
  border-radius: 12px !important;
  border: 2px solid #e5e7eb !important;
  font-size: 16px !important;
  transition: all 0.3s !important;
  box-shadow: none !important;
}

.game-search-input :deep(.ant-select-selector:hover) {
  border-color: #60a5fa !important; /* 鼠标悬停边框颜色 */
}

.game-search-input :deep(.ant-select-focused .ant-select-selector),
.game-search-input :deep(.ant-select-selector:focus) {
  border-color: #0ba2e3 !important; /* 聚焦边框颜色 */
  box-shadow: 0 0 0 3px rgba(11, 162, 227, 0.1) !important; /* 聚焦阴影 */
}

/* Ant Design Select Option 样式 */
.ant-select-item-option-content {
  display: flex;
  align-items: center;
}
.ant-select-item-option-content img {
  margin-right: 8px;
}

.search-icon {
  color: #9ca3af;
  font-size: 18px;
}

/* 数据维度区域和时间维度的共同样式 */
.dimension-list { /* 新增的列表容器样式 */
  display: flex;
  flex-direction: column;
  gap: 15px; /* 每行之间的垂直间距 */
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
}

.dimension-section {
  padding: 16px 20px; /* 内部填充 */
  background: #fff;
  border-radius: 8px; /* 恢复一点圆角，看起来像一个独立的卡片 */
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08); /* 恢复一些柔和的阴影 */
  border: 1px solid #e0e0e0; /* 恢复边框 */
}


.dimension-row-single {
  display: flex;
  align-items: center;
}

.dimension-label-single {
  display: flex;
  align-items: center;
  font-size: 15px;
  font-weight: 600;
  color: #333;
  margin-right: 20px;
  flex-shrink: 0;
}

.dimension-label-bar {
  width: 4px;
  height: 18px;
  background: #2d5be3;
  border-radius: 2px;
  margin-right: 8px;
}

.dimension-checkboxes-group {
  display: flex;
  flex-wrap: wrap; /* 允许复选框换行 */
  gap: 15px; /* 复选框之间的水平间距 */
  flex: 1;
  align-items: center;
}

.dimension-checkboxes-item {
  flex-shrink: 0; /* 防止子项在空间不足时收缩 */
  display: flex;
  align-items: center;
}

/* Ant Design Checkbox 样式覆盖 */
.dimension-checkboxes-item :deep(.ant-checkbox-wrapper) {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #555;
}

.dimension-checkboxes-item :deep(.ant-checkbox-inner) {
  width: 18px;
  height: 18px;
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  background-color: #fff;
  transition: all 0.2s;
  box-sizing: border-box;
}

.dimension-checkboxes-item :deep(.ant-checkbox-checked .ant-checkbox-inner) {
  background-color: #1890ff;
  border-color: #1890ff;
}

.dimension-checkboxes-item :deep(.ant-checkbox:hover .ant-checkbox-inner) {
  border-color: #1890ff;
}

.dimension-checkboxes-item :deep(.ant-checkbox-checked::after) {
  border: 2px solid transparent;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg) scale(1) translate(-50%, -50%);
  position: absolute;
  top: 50%;
  left: 50%;
  width: 5.71428571px;
  height: 9.14285714px;
  content: '';
  display: table;
  border: 2px solid #fff;
  border-top: 0;
  border-left: 0;
  transform: rotate(45deg) scale(1) translate(-50%, -50%);
  opacity: 1;
  transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46) 0.1s;
}

/* 日期范围选择器样式 */
.date-range-picker-section {
  padding: 16px 20px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.08);
  border: 1px solid #e0e0e0;
  display: flex;
  justify-content: center; /* 居中日期选择器 */
}
/* Ant Design 日期选择器样式覆盖 */
.date-range-picker-section :deep(.ant-picker) {
  height: 40px;
  border-radius: 8px;
  border-color: #d9d9d9;
}
.date-range-picker-section :deep(.ant-picker:hover) {
  border-color: #1890ff;
}
.date-range-picker-section :deep(.ant-picker-focused) {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}


/* 第四步报告预览的样式 */
.report-preview-header-input { /* 修改了类名以区分，并为 input 调整样式 */
  width: 100%;
  text-align: center;
  margin-bottom: 30px;
  display: flex; /* 使用 flex 布局居中 input */
  justify-content: center;
}

.report-name-input { /* 新增 input 样式 */
  font-size: 20px;
  font-weight: bold;
  color: #333;
  width: 80%; /* 控制输入框宽度 */
  max-width: 400px; /* 最大宽度 */
  text-align: center; /* 文本居中 */
  border: none;
  border-bottom: 2px solid #e0e0e0; /* 底部边框 */
  padding: 5px 10px;
  outline: none; /* 移除默认聚焦轮廓 */
  transition: border-color 0.3s ease;
}

.report-name-input:focus {
  border-color: #0ba2e3; /* 聚焦时边框颜色 */
}


.selected-items-grid {
  display: grid;
  grid-template-columns: 1fr; /* 默认单列 */
  gap: 24px; /* 组之间的垂直间距 */
  width: 100%;
  max-width: 650px; /* **这里是修改点，增加最大宽度** */
  margin: 0 auto; /* 居中显示 */
}

@media (min-width: 768px) {
  .selected-items-grid {
    grid-template-columns: repeat(3, 1fr); /* 桌面端三列布局 */
    gap: 20px; /* 组之间的间距 */
  }
}

/* 为已选竞品、已选数据、时间维度共同的样式 */
.selected-item-group {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 10px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  align-items: flex-start; /* 内容左对齐 */
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.group-label {
  font-size: 14px;
  color: #64748b;
  margin-bottom: 12px;
  font-weight: 500;
}

.group-tags {
  display: flex;
  flex-wrap: wrap; /* 允许标签换行 */
  gap: 8px; /* 标签之间的间距 */
  width: 100%; /* 确保标签组占据整个宽度 */
}

.selected-tag {
  display: inline-flex;
  align-items: center;
  background-color: #fff;
  border: 1px solid #cbd5e1;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 14px;
  color: #334155;
  gap: 6px; /* 图标和文字的间距 */
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  transition: all 0.2s ease-in-out;
}

.selected-tag:hover {
  border-color: #a78bfa;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.tag-icon {
  width: 20px;
  height: 20px;
  border-radius: 4px;
  object-fit: cover;
}

.tag-close-icon {
  font-size: 12px;
  color: #9ca3af;
  cursor: pointer;
  margin-left: 4px;
  transition: color 0.2s;
}

.tag-close-icon:hover {
  color: #ef4444;
}

/* 移除原有的 processing-content 样式，因为现在它被用作 flex 容器 */
.processing-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  padding-top: 20px; /* 调整内容顶部内边距 */
}

.modal-footer-new {
  padding: 20px 40px 30px 40px;
  border-top: 1px solid #f1f5f9;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #fafbfc;
}

.prev-btn {
  height: 40px;
  padding: 0 24px;
  border-radius: 8px;
  border: 1px solid #d1d5db;
  background: #fff;
  color: #6b7280;
  font-weight: 500;
  transition: all 0.2s;
}

.prev-btn:hover {
  border-color: #9ca3af;
  color: #374151;
}

.next-btn {
  height: 40px;
  padding: 0 24px;
  border-radius: 8px;
  background: #0ba2e3; /* 蓝色背景 */
  border: none;
  color: #fff;
  font-weight: 500;
  transition: all 0.2s;
}

.next-btn:hover {
  background: #0981b4; /* 深蓝色 hover */
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(11, 162, 227, 0.3);
}

@media (max-width: 600px) {
  .tab-title {
    font-size: 15px;
    max-width: 80px;
  }
  .download-btn .download-text {
    display: none;
  }
  .download-btn {
    min-width: 32px;
    padding: 0 4px;
  }
  .edit-btn {
    font-size: 16px;
    padding: 0 2px;
  }
  
  .product-cards {
    gap: 20px;
    flex-direction: column; /* 小屏幕下产品卡片垂直排列 */
    align-items: center;
  }
  
  .product-card-new本品,
  .product-card-new竞品 { /* 注意这里可能需要根据实际调整，如果竞品列表也需要单独flex item */
    width: 140px;
    padding: 16px 12px;
  }
  
  .steps-container {
    gap: 30px;
    flex-wrap: wrap;
  }
  
  .modal-content-new {
    padding: 30px 20px;
  }
  
  .modal-footer-new {
    padding: 16px 20px 24px 20px;
  }

  .product-selection-horizontal {
    flex-direction: column; /* 小屏幕下整体垂直排列 */
    align-items: center;
    gap: 20px;
  }

  .left-search {
    align-items: center;
    min-width: unset;
    width: 100%;
  }

  .right-panel {
    min-width: unset;
    width: 100%;
  }

  .dimension-list { /* 小屏幕下列表间距调整 */
    gap: 10px;
  }

  .dimension-section {
    max-width: 100%; /* 小屏幕下适应宽度 */
    padding: 12px 15px; /* 调整小屏幕内边距 */
  }

  .dimension-label-single {
    margin-right: 15px; /* 调整小屏幕间距 */
  }

  .dimension-checkboxes-group {
    gap: 15px; /* 调整小屏幕复选框间距 */
  }

  .report-name-input {
    font-size: 18px; /* 小屏幕字体略小 */
    width: 100%; /* 小屏幕宽度调整 */
  }
}
</style>