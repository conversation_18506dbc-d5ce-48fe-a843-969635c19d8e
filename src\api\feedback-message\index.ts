import { defHttp } from '/@/utils/http/axios';
import { formDataHttp } from '/@/utils/http/axios';
enum Api {
  getPageWorkOrderSave = '/workOrder/save',
  getPageWorkOrder = '/workOrder/pageWorkOrder',
  getChange2COMPLETEDWorkOrder = '/workOrder/change2COMPLETED',
}

/**
 * @description: 获取反馈信息保存
 */
export function getWorkOrderSaveApi(params) {
  return defHttp.post({
    url: Api.getPageWorkOrderSave,
    params,
  });
}
/**
 * @description: 获取反馈对话列表
 */
export function getPageWorkOrderApi(params) {
  return defHttp.get({
    url: Api.getPageWorkOrder,
    params,
  });
}
/**
 * @description: 删除反馈对话
 */

export function deleteWorkOrderApi(params) {
  return defHttp.delete({
    url: `/workOrder/delete/${params}`,
    // params,
  });
}
/**
 * @description: 修改反馈对话状态
 */

export function changrTypeWorkOrderApi(params) {
  return defHttp.get({
    url: Api.getChange2COMPLETEDWorkOrder,
    params,
  });
}
