import { defHttp } from '/@/utils/http/axios';
enum Api {
  crawlerForm = '/crawler/crawlerForm',
  // findAllCrawlerForm = 'crawler/findAllCrawlerForm',
  saveTablePlan = 'plan/save',
  findByUserId = 'crawler/findCrawlerFormByUserId',
  findCrawlerFormByPage = 'crawler/findCrawlerFormByPage',
  deleteCrawlerForm = 'crawler/deleteCrawlerForm',
  findPlanTableByPage = 'plan/findPlanTableByPage',
  deletePlanTable = 'plan/deletePlanTable',
  // logicDeletePlan = '/plan/logicDelete',
  // logicDeleteCrawlerForm = '/crawler/logicDelete/',
  updatePlanTable = 'plan/updatePlanTable'
}

// 信息来源渠道-保存
export function crawlerFormApi(params) {
    return defHttp.post({
      url: Api.crawlerForm,
      data: params, // 确保传递的是 data 而不是 params
    });
  }

// 信息来源渠道-查询  用于把返回的渠道名称绑定到新建舆情方案配置中的监测渠道选项上
export function findByUserIdApi(params){
  return defHttp.get({
    url: Api.findByUserId,
    params
  })
}

// 信息来源渠道-条件查询
export function findCrawlerFormByPageApi(params){
  return defHttp.post({
    url: Api.findCrawlerFormByPage,
    data: params
  })
}

// 信息来源渠道-删除
export function deleteCrawlerFormApi(id){
  return defHttp.delete({
    url: `${Api.deleteCrawlerForm}/${id}`, // 路径参数拼接
  })
}

// 配置监控方案-查询
// export function findAllCrawlerFormApi(){
//     return defHttp.get({
//         url:Api.findAllCrawlerForm
//     })
// }

// 新建舆情方案配置
export function saveTablePlanApi(params){
    return defHttp.post({
        url:Api.saveTablePlan,
        data:params
    })
}

// 配置监控方案-条件查询
export function findPlanTableByPageApi(params){
  return defHttp.post({
    url:Api.findPlanTableByPage,
    data:params
  })
}

//配置监控方案-删除
export function deletePlanTableApi(id){
  return defHttp.delete({
    url:`${Api.deletePlanTable}/${id}`
  })
}

// 配置监控方案-修改
export function updatePlanTableApi(params){
  return defHttp.post({
    url:Api.updatePlanTable,
    data:params
  })
}


