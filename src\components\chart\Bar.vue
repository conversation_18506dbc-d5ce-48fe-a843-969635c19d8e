<template>
  <div ref="chartRef" :style="{ height, width }"></div>
</template>
<script lang="ts">
  import { defineComponent, PropType, ref, Ref, reactive, watchEffect } from 'vue';
  import { useECharts } from '/@/hooks/web/useECharts';
  import { cloneDeep } from 'lodash-es';
  export default defineComponent({
    name: 'bar',
    props: {
      chartData: {
        type: Array as PropType<Array<{ name: string; value: number }>>,
        default: () => [],
      },
      option: {
        type: Object,
        default: () => ({}),
      },
      width: {
        type: String as PropType<string>,
        default: '100%',
      },
      height: {
        type: String as PropType<string>,
        default: 'calc(100vh - 78px)',
      },
      // update-begin--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
      seriesColor: {
        type: String,
        default: '#1890ff',
      },
      // update-end--author:liaozhiyang---date:20240407---for：【QQYUN-8762】首页默认及echars颜色调整
    },
    setup(props) {
      const chartRef = ref<HTMLDivElement | null>(null);
      const { setOptions, echarts } = useECharts(chartRef as Ref<HTMLDivElement>);
      const option = reactive({
        tooltip: {
          trigger: 'axis' as const,
          axisPointer: {
            type: 'shadow' as const,
            label: {
              show: true,
              backgroundColor: '#333',
            },
          },
          backgroundColor: 'rgba(50,50,50,0.9)',
          borderRadius: 8,
          textStyle: {
            color: '#fff',
            fontSize: 14,
          },
          padding: 12,
        },
        xAxis: {
          type: 'category' as const,
          data: [] as any[],
          axisLine: {
            lineStyle: {
              color: '#dbeafe',
              width: 2,
            },
          },
          axisLabel: {
            color: '#888',
            fontWeight: 500,
            fontSize: 14,
          },
          axisTick: {
            show: false,
          },
        },
        yAxis: {
          type: 'value' as const,
          splitLine: {
            lineStyle: {
              color: '#e5e7eb',
              type: 'dashed' as const,
            },
          },
          axisLine: {
            show: false,
          },
          axisLabel: {
            color: '#888',
            fontWeight: 500,
            fontSize: 14,
          },
          axisTick: {
            show: false,
          },
        },
        series: [
          {
            name: 'bar',
            type: 'bar' as const,
            data: [] as any[],
            barWidth: 90,
            itemStyle: {
              borderRadius: [12, 12, 6, 6],
              shadowColor: 'rgba(0,0,0,0.08)',
              shadowBlur: 8,
              shadowOffsetY: 2,
              color: (params: any) => {
                if (Array.isArray(props.seriesColor)) {
                  return props.seriesColor[params.dataIndex % props.seriesColor.length];
                } else {
                  return {
                    type: 'linear',
                    x: 0,
                    y: 0,
                    x2: 0,
                    y2: 1,
                    colorStops: [
                      { offset: 0, color: props.seriesColor },
                      { offset: 1, color: '#fffbe6' },
                    ],
                  };
                }
              },
            },
            label: {
              show: true,
              position: 'outside',
              color: '#222',
              fontWeight: 600,
              fontSize: 15,
              formatter: '{c}',
            },
            emphasis: {
              itemStyle: {
                shadowColor: 'rgba(60,120,240,0.18)',
                shadowBlur: 16,
              },
            },
          },
        ],
        grid: {
          left: 30,
          right: 20,
          top: 30,
          bottom: 30,
          containLabel: true,
        },
      });

      watchEffect(() => {
        props.chartData && initCharts();
      });

      function initCharts() {
        if (props.option) {
          Object.assign(option, cloneDeep(props.option));
        }
        let seriesData = props.chartData.map((item) => {
          return item.value;
        });
        let xAxisData = props.chartData.map((item) => {
          return item.name;
        });
        option.series[0].data = seriesData;
        option.xAxis.data = xAxisData;
        setOptions(option);
      }
      return { chartRef };
    },
  });
</script>
